#!/bin/bash

# 获取脚本所在目录
SCRIPT_DIR="$( cd "$( dirname "${BASH_SOURCE[0]}" )" && pwd )"

# 切换到脚本所在目录
cd "$SCRIPT_DIR"

# 创建log目录(如果不存在)
LOG_DIR="$SCRIPT_DIR/log"
mkdir -p "$LOG_DIR"

# 设置日志文件
LOG_DATE=$(date +%Y%m%d)
LOG_FILE="$LOG_DIR/weekly_stock_process_${LOG_DATE}.log"

# ServerChan配置
SERVERCHAN_KEY="sctp5099tkrhuzdrsrepnwn352fyftq"

# ServerChan发送函数
function sc_send() {
    local title=$1
    local message=$2
    local key=$3

    # URL编码函数
    urlencode() {
        local string="${1}"
        local strlen=${#string}
        local encoded=""
        local pos c o

        for (( pos=0 ; pos<strlen ; pos++ )); do
            c=${string:$pos:1}
            case "$c" in
                [-_.~a-zA-Z0-9] ) o="${c}" ;;
                * )               printf -v o '%%%02x' "'$c"
            esac
            encoded+="${o}"
        done
        echo "${encoded}"
    }

    # URL编码参数
    local encoded_title=$(urlencode "$title")
    local encoded_message=$(urlencode "$message")
    local postdata="text=${encoded_title}&desp=${encoded_message}"

    # 判断 key 是否以 "sctp" 开头，选择不同的 URL
    if [[ "$key" =~ ^sctp([0-9]+)t ]]; then
        num=${BASH_REMATCH[1]}
        url="https://${num}.push.ft07.com/send/${key}.send"
    else
        url="https://sctapi.ftqq.com/${key}.send"
    fi

    # 发送请求
    result=$(curl -X POST -s -o /dev/null -w "%{http_code}" "$url" \
        --header "Content-type: application/x-www-form-urlencoded" \
        --data "$postdata")
    
    echo "$result"
}

# 记录开始时间
START_TIME=$(date '+%Y-%m-%d %H:%M:%S')
echo "===========================================" >> "$LOG_FILE"
echo "开始执行周股票数据处理流程" >> "$LOG_FILE"
echo "开始时间: $START_TIME" >> "$LOG_FILE"
echo "===========================================" >> "$LOG_FILE"

# 检查今天是否为交易日
IS_TRADE_DAY=$(/home/<USER>/anaconda3/bin/python -c "import trade_calendar; print(trade_calendar.is_trade_day())")

# 默认结果为成功
RESULT=0

if [ "$IS_TRADE_DAY" = "True" ]; then
    echo "今天是交易日,继续执行处理流程" >> "$LOG_FILE"
    
    # 运行Python脚本
    /home/<USER>/anaconda3/bin/python 20_weekly_stock_process.py >> "$LOG_FILE" 2>&1
    
    # 记录结束时间和状态
    RESULT=$?
    END_TIME=$(date '+%Y-%m-%d %H:%M:%S')
    echo "===========================================" >> "$LOG_FILE"
    
    if [ $RESULT -eq 0 ]; then
        echo "周股票数据处理流程执行成功" >> "$LOG_FILE"
    else
        ERROR_MSG="周股票数据处理流程执行失败，错误代码: $RESULT"
        echo "周股票数据处理流程执行失败,错误代码: $RESULT" >> "$LOG_FILE"
        sc_send "周股票数据处理失败" "$ERROR_MSG" "$SERVERCHAN_KEY"
    fi
else
    SKIP_MSG="今天 $(date '+%Y-%m-%d') 不是交易日,跳过处理流程"
    echo "$SKIP_MSG" >> "$LOG_FILE"
    RESULT=0
fi

echo "结束时间: $(date '+%Y-%m-%d %H:%M:%S')" >> "$LOG_FILE"
echo "===========================================" >> "$LOG_FILE"

exit $RESULT 