#!/usr/bin/env python
# -*- coding: utf-8 -*-

import os
import sys
import subprocess
from datetime import datetime
from log_config import setup_logger, log_progress, log_error, send_notification

# 获取脚本所在目录和脚本名称
base_dir = os.path.dirname(os.path.abspath(__file__))
script_name = os.path.splitext(os.path.basename(__file__))[0]

# 配置日志 - 包括日期在文件名中
logger = setup_logger(script_name, include_date_in_filename=True)

def run_script(script_path, script_name, timeout=3600, critical=True):
    """
    运行指定的Python脚本

    Args:
        script_path: 脚本的完整路径
        script_name: 脚本的名称(用于日志和通知)
        timeout: 超时时间(秒)，默认1小时
        critical: 是否为关键脚本，失败时是否中断流程

    Returns:
        dict: 包含执行结果的字典
    """
    import time

    start_time = time.time()
    log_progress(logger, f"开始执行 {script_name}")

    # 检查脚本文件是否存在
    if not os.path.exists(script_path):
        error_msg = f"脚本文件不存在: {script_path}"
        log_error(logger, error_msg)
        return {
            "success": False,
            "duration": 0,
            "output": "",
            "error": error_msg,
            "critical": critical
        }

    try:
        # 使用subprocess运行脚本，添加超时控制
        result = subprocess.run(
            [sys.executable, script_path],
            check=True,
            capture_output=True,
            text=True,
            timeout=timeout
        )

        duration = time.time() - start_time
        log_progress(logger, f"{script_name} 执行成功，耗时 {duration:.2f} 秒")

        # 记录输出的前几行（避免日志过长）
        if result.stdout:
            output_lines = result.stdout.strip().split('\n')
            if len(output_lines) > 5:
                logger.debug(f"输出前5行: {chr(10).join(output_lines[:5])}...")
            else:
                logger.debug(f"输出: {result.stdout}")

        return {
            "success": True,
            "duration": duration,
            "output": result.stdout,
            "error": result.stderr,
            "critical": critical
        }
    except subprocess.TimeoutExpired as e:
        duration = time.time() - start_time
        error_msg = f"{script_name} 执行超时 ({timeout}秒)"
        log_error(logger, f"{error_msg}，已执行 {duration:.2f} 秒")

        # 只对关键脚本发送超时通知
        if critical:
            send_notification(
                f"{script_name} 执行超时:\n超时时间: {timeout}秒\n已执行时间: {duration:.2f}秒",
                f"股票数据处理超时 - {script_name}",
                "股票数据|执行超时"
            )

        return {
            "success": False,
            "duration": duration,
            "output": "",
            "error": error_msg,
            "critical": critical
        }

    except subprocess.CalledProcessError as e:
        duration = time.time() - start_time
        log_error(logger, f"{script_name} 执行失败 (耗时 {duration:.2f} 秒): {e}")
        log_error(logger, f"错误输出: {e.stderr}")

        # 只对关键脚本发送失败通知
        if critical:
            send_notification(
                f"{script_name} 执行过程中发生错误:\n{e.stderr}",
                f"股票数据处理失败 - {script_name}",
                "股票数据|处理失败"
            )

        return {
            "success": False,
            "duration": duration,
            "output": e.stdout or "",
            "error": e.stderr or str(e),
            "critical": critical
        }

    except Exception as e:
        duration = time.time() - start_time
        error_msg = f"{script_name} 执行过程中发生异常: {str(e)}"
        log_error(logger, f"{error_msg} (耗时 {duration:.2f} 秒)")

        # 只对关键脚本发送异常通知
        if critical:
            send_notification(
                f"{script_name} 执行过程中发生异常:\n{str(e)}",
                f"股票数据处理异常 - {script_name}",
                "股票数据|系统异常"
            )

        return {
            "success": False,
            "duration": duration,
            "output": "",
            "error": error_msg,
            "critical": critical
        }

def main():
    """主函数,按顺序执行所有脚本"""
    # 获取当前脚本所在目录的绝对路径
    base_dir = os.path.dirname(os.path.abspath(__file__))

    # 定义需要执行的脚本列表（优化配置）
    scripts = [
        {
            "path": os.path.join(base_dir, "1_stock_basic.py"),
            "name": "股票基本信息获取",
            "timeout": 300,  # 5分钟超时
            "critical": True  # 关键脚本，失败时停止后续执行
        },
        {
            "path": os.path.join(base_dir, "2_stock_daily.py"),
            "name": "股票日线数据获取",
            "timeout": 3600,  # 1小时超时
            "critical": True  # 关键脚本
        },
        {
            "path": os.path.join(base_dir, "3_stock_indicators.py"),
            "name": "股票指标计算",
            "timeout": 1800,  # 30分钟超时
            "critical": True  # 关键脚本
        },
        {
            "path": os.path.join(base_dir, "4_macd_divergence.py"),
            "name": "MACD背离分析",
            "timeout": 600,  # 10分钟超时
            "critical": True  # 关键脚本
        },
        {
            "path": os.path.join(base_dir, "5_export_macd_data.py"),
            "name": "MACD数据导出",
            "timeout": 300,  # 5分钟超时
            "critical": False  # 非关键脚本，失败时继续执行
        }
    ]
    
    # 记录开始时间
    start_time = datetime.now()
    log_progress(logger, "=== 日常股票数据处理流程开始 ===")
    log_progress(logger, f"开始时间: {start_time.strftime('%Y-%m-%d %H:%M:%S')}")
    log_progress(logger, f"计划执行 {len(scripts)} 个脚本")

    # 存储执行结果
    results = []
    critical_failure = False

    # 依次执行脚本
    for i, script in enumerate(scripts, 1):
        log_progress(logger, f"[{i}/{len(scripts)}] 准备执行: {script['name']}")

        result = run_script(
            script["path"],
            script["name"],
            timeout=script.get("timeout", 3600),
            critical=script.get("critical", True)
        )

        results.append({
            "name": script["name"],
            "result": result
        })

        # 如果关键脚本执行失败,中断流程
        if not result["success"] and result["critical"]:
            critical_failure = True
            log_error(logger, f"关键脚本 {script['name']} 执行失败,中断整个处理流程")
            break
        elif not result["success"]:
            # 非关键脚本失败，记录警告但继续执行
            log_progress(logger, f"非关键脚本 {script['name']} 执行失败，继续执行后续脚本")

        log_progress(logger, f"[{i}/{len(scripts)}] {script['name']} 完成")
    
    # 记录结束时间并计算总耗时
    end_time = datetime.now()
    total_duration = end_time - start_time

    # 生成详细的执行报告
    successful_scripts = [item for item in results if item["result"]["success"]]
    failed_scripts = [item for item in results if not item["result"]["success"]]
    critical_failed = [item for item in failed_scripts if item["result"]["critical"]]

    log_progress(logger, "=== 日常股票数据处理流程完成 ===")
    log_progress(logger, f"结束时间: {end_time.strftime('%Y-%m-%d %H:%M:%S')}")
    log_progress(logger, f"总耗时: {total_duration.total_seconds()/60:.2f} 分钟")
    log_progress(logger, f"执行结果: 成功 {len(successful_scripts)}/{len(results)} 个脚本")

    # 详细的脚本执行报告
    log_progress(logger, "\n=== 脚本执行详情 ===")
    for item in results:
        status = "✅成功" if item["result"]["success"] else "❌失败"
        critical_mark = "🔴" if item["result"]["critical"] else "🟡"
        duration_str = f"{item['result']['duration']:.2f}秒"

        log_progress(logger, f"{critical_mark} {item['name']}: {status} ({duration_str})")

        if not item["result"]["success"]:
            error_preview = item["result"]["error"][:100] + "..." if len(item["result"]["error"]) > 100 else item["result"]["error"]
            log_progress(logger, f"   错误: {error_preview}")

    # 判断整体执行状态
    if not critical_failure and len(failed_scripts) == 0:
        log_progress(logger, "🎉 所有脚本执行成功！")
        return True
    elif not critical_failure:
        log_progress(logger, f"⚠️  部分非关键脚本失败，但整体流程完成")
        log_progress(logger, f"失败的非关键脚本: {', '.join([item['name'] for item in failed_scripts])}")
        return True
    else:
        log_error(logger, "❌ 关键脚本失败，流程中断")
        log_error(logger, f"失败的关键脚本: {', '.join([item['name'] for item in critical_failed])}")
        return False

if __name__ == "__main__":
    import sys

    try:
        success = main()
        if not success:
            log_error(logger, "日常股票数据处理流程失败")
            sys.exit(1)
        else:
            log_progress(logger, "日常股票数据处理流程成功完成")

    except KeyboardInterrupt:
        log_error(logger, "用户中断了处理流程")
        sys.exit(1)

    except Exception as e:
        error_msg = str(e)
        log_error(logger, f"处理流程发生未预期的异常: {error_msg}")
        send_notification(
            message=f"处理流程发生未预期的异常:\n{error_msg}",
            title="股票数据处理发生严重异常",
            tags="股票数据|系统异常"
        )
        sys.exit(1)