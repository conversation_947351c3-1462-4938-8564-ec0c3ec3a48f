"""
优化版股票数据入库程序 - 增强数据完整性与稳定性
Created on 2024-05-30
@author: 优化版本
"""
import pandas as pd
import tushare as ts
from sqlalchemy import create_engine, text
from sqlalchemy.exc import SQLAlchemyError
import os
from dotenv import load_dotenv
from contextlib import contextmanager
import configparser
from log_config import setup_logger, log_progress, log_error, send_notification,log_warning

# 初始化环境变量
load_dotenv()

# 配置日志系统 - 使用脚本文件名而不是__name__
script_name = os.path.splitext(os.path.basename(__file__))[0]
logger = setup_logger(script_name)

def load_config():
    """加载配置文件"""
    config = configparser.ConfigParser()
    config_path = os.path.join(os.path.dirname(__file__), 'config.ini')

    if not os.path.exists(config_path):
        log_error(logger, f"配置文件不存在: {config_path}")
        raise FileNotFoundError(f"配置文件不存在: {config_path}")

    try:
        config.read(config_path, encoding='utf-8')
        log_progress(logger, f"成功加载配置文件: {config_path}")
        return config
    except Exception as e:
        log_error(logger, f"加载配置文件失败: {str(e)}")
        raise

def get_config_value(section, key, fallback=None, value_type='str'):
    """
    获取配置值,支持类型转换

    Args:
        section: 配置节
        key: 配置键
        fallback: 默认值 (如果为None且配置不存在则抛出异常)
        value_type: 值类型 ('str', 'int', 'float', 'bool')

    Returns:
        配置值

    Raises:
        ValueError: 当配置不存在且没有提供fallback时
    """
    try:
        if value_type == 'int':
            return config.getint(section, key, fallback=fallback)
        elif value_type == 'float':
            return config.getfloat(section, key, fallback=fallback)
        elif value_type == 'bool':
            return config.getboolean(section, key, fallback=fallback)
        else:
            return config.get(section, key, fallback=fallback)
    except Exception as e:
        if fallback is None:
            error_msg = f"必需的配置项 {section}.{key} 不存在或无效: {str(e)}"
            log_error(logger, error_msg)
            raise ValueError(error_msg)
        else:
            log_warning(logger, f"获取配置值失败 {section}.{key}: {str(e)},使用默认值 {fallback}")
            return fallback

# 加载配置
config = load_config()

def validate_configuration():
    """验证配置完整性"""
    required_configs = [
        ('database', 'host'),
        ('database', 'port'),
        ('database', 'database'),
        ('database', 'user'),
        ('database', 'password'),
        ('tushare', 'token'),
        ('database.pool', 'pool_size'),
        ('database.pool', 'max_overflow'),
        ('database.pool', 'pool_recycle'),
        ('database.pool', 'pool_pre_ping')
    ]

    missing_configs = []
    for section, key in required_configs:
        try:
            value = config.get(section, key)
            if not value or value.strip() == '':
                missing_configs.append(f"{section}.{key}")
        except Exception:
            missing_configs.append(f"{section}.{key}")

    if missing_configs:
        error_msg = f"缺少必需的配置项: {', '.join(missing_configs)}"
        log_error(logger, error_msg)
        raise ValueError(error_msg)

    log_progress(logger, "股票基本信息处理配置验证通过")
    return True

# 验证配置
validate_configuration()

# =============================================================================
# 配置管理 - 从config.ini文件读取配置
# =============================================================================
# 这些配置参数从config.ini文件读取,包括数据库连接和API密钥配置
# 避免了硬编码敏感信息,提高了安全性

def get_db_config():
    """从配置文件获取数据库配置"""
    return {
        'user': get_config_value('database', 'user'),
        'password': get_config_value('database', 'password'),
        'host': get_config_value('database', 'host'),
        'port': get_config_value('database', 'port'),
        'database': get_config_value('database', 'database'),
        'charset': 'utf8mb4'
    }

def get_tushare_token():
    """从配置文件获取Tushare Token"""
    return get_config_value('tushare', 'token')

# ServerChan配置从log_config导入,不再重复定义

# 表结构定义
TABLE_SCHEMA = """
CREATE TABLE IF NOT EXISTS `stock_basic` (
    `ts_code` VARCHAR(10) NOT NULL COMMENT 'TS代码',
    `stock_code` VARCHAR(6) NOT NULL COMMENT '股票代码',
    `name` VARCHAR(50) NOT NULL COMMENT '股票名称',
    `area` VARCHAR(50) COMMENT '地域',
    `industry` VARCHAR(50) COMMENT '所属行业',
    `market` VARCHAR(20) COMMENT '市场类型',
    `list_date` DATE NOT NULL COMMENT '上市日期',
    `is_hs` VARCHAR(5) COMMENT '是否沪深港通标的',
    `created_at` TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    `updated_at` TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    PRIMARY KEY (`ts_code`),
    UNIQUE KEY `idx_stock_code` (`stock_code`),
    KEY `idx_industry` (`industry`),
    KEY `idx_list_date` (`list_date`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='股票基本信息表';
"""

# 全局数据库引擎
_DB_ENGINE = None

def get_db_engine():
    """获取数据库引擎(单例模式)"""
    global _DB_ENGINE
    if _DB_ENGINE is None:
        # 从配置文件获取数据库配置
        db_config = get_db_config()

        # 从配置文件获取连接池参数
        pool_size = get_config_value('database.pool', 'pool_size', 10, 'int')
        max_overflow = get_config_value('database.pool', 'max_overflow', 20, 'int')
        pool_recycle = get_config_value('database.pool', 'pool_recycle', 3600, 'int')
        pool_pre_ping = get_config_value('database.pool', 'pool_pre_ping', True, 'bool')
        echo = get_config_value('database.pool', 'echo', False, 'bool')

        _DB_ENGINE = create_engine(
            f"mysql+pymysql://{db_config['user']}:{db_config['password']}@"
            f"{db_config['host']}:{db_config['port']}/{db_config['database']}?charset={db_config['charset']}",
            pool_size=pool_size,
            max_overflow=max_overflow,
            pool_recycle=pool_recycle,
            pool_pre_ping=pool_pre_ping,
            echo=echo  # SQL日志开关
        )
    return _DB_ENGINE

@contextmanager
def db_connection():
    """数据库连接上下文管理"""
    engine = get_db_engine()
    conn = None
    try:
        conn = engine.connect()
        yield conn
    except SQLAlchemyError as e:
        log_error(logger, f"数据库连接失败: {str(e)}")
        raise
    finally:
        if conn:
            conn.close()

def init_database():
    """初始化数据库表结构"""
    try:
        with db_connection() as conn:
            conn.execute(text(TABLE_SCHEMA))
            logger.info("数据库表初始化完成")
    except Exception as e:
        log_error(logger, f"数据库初始化失败: {str(e)}")
        raise

def validate_stock_data(df):
    """数据校验与清洗"""
    if df.empty:
        log_error(logger, "接收到空的股票数据")
        return df

    # 创建数据框的副本
    df = df.copy()

    # 重命名 symbol 列为 stock_code
    df = df.rename(columns={'symbol': 'stock_code'})

    # 批量数据处理
    try:
        # 转换日期格式
        df['list_date'] = pd.to_datetime(df['list_date'], format='%Y%m%d', errors='coerce').dt.date

        # 批量处理特殊字符和空值
        df['name'] = df['name'].str.replace(r'[*]', '', regex=True)

        # 批量填充空值
        fill_values = {
            'industry': '未知行业',
            'market': '未知市场',
            'area': '未知地区'
        }
        df = df.fillna(fill_values)

        # 删除重复项
        df = df.drop_duplicates(subset=['ts_code'], keep='first')

        # 过滤无效数据
        valid_df = df.dropna(subset=['ts_code', 'stock_code', 'list_date'])

        if len(valid_df) < len(df):
            log_progress(logger, f"数据清洗:从 {len(df)} 条减少到 {len(valid_df)} 条有效记录")

        return valid_df

    except Exception as e:
        log_error(logger, f"数据验证失败: {str(e)}")
        raise

def get_stock_data():
    """获取股票数据(带重试机制)"""
    import time

    max_retries = 3
    retry_delay = 2  # 重试间隔秒数

    for attempt in range(max_retries):
        try:
            log_progress(logger, f"开始获取股票基本信息数据 (尝试 {attempt + 1}/{max_retries})")

            # 使用配置中的Tushare Token
            tushare_token = get_tushare_token()
            pro = ts.pro_api(tushare_token)
            df = pro.stock_basic(
                exchange='',
                list_status='L',
                fields='ts_code,symbol,name,area,industry,market,list_date,is_hs'
            )

            if df.empty:
                raise ValueError("API返回空数据")

            log_progress(logger, f"成功获取 {len(df)} 条原始股票数据")
            validated_df = validate_stock_data(df)

            if validated_df.empty:
                raise ValueError("数据验证后无有效记录")

            log_progress(logger, f"数据验证完成,有效记录 {len(validated_df)} 条")
            return validated_df

        except Exception as e:
            error_msg = f"第 {attempt + 1} 次尝试失败: {str(e)}"

            if attempt < max_retries - 1:
                log_progress(logger, f"{error_msg},{retry_delay}秒后重试...")
                time.sleep(retry_delay)
                retry_delay *= 2  # 指数退避
                continue
            else:
                log_error(logger, f"所有重试均失败: {error_msg}")
                raise RuntimeError(f"数据获取失败: {str(e)}")

def save_stock_data(df):
    """安全保存数据到数据库"""
    import time

    if df.empty:
        log_progress(logger, "无有效数据需要保存")
        return

    start_time = time.time()
    log_progress(logger, f"开始保存 {len(df)} 条股票数据到数据库")

    try:
        with db_connection() as conn:
            # 使用事务处理
            with conn.begin():
                # 清空历史数据
                truncate_start = time.time()
                conn.execute(text("TRUNCATE TABLE stock_basic"))
                truncate_time = time.time() - truncate_start
                log_progress(logger, f"清空历史数据完成,耗时 {truncate_time:.2f} 秒")

                # 批量插入数据
                insert_start = time.time()
                df.to_sql(
                    name='stock_basic',
                    con=conn,
                    if_exists='append',
                    index=False,
                    chunksize=500,  # 优化批次大小
                    method='multi'
                )
                insert_time = time.time() - insert_start

            total_time = time.time() - start_time
            log_progress(logger, f"数据保存完成: {len(df)} 条记录")
            log_progress(logger, f"性能统计: 插入耗时 {insert_time:.2f}秒, 总耗时 {total_time:.2f}秒")
            log_progress(logger, f"写入速度: {len(df)/total_time:.0f} 条/秒")

    except SQLAlchemyError as e:
        log_error(logger, f"数据写入失败: {str(e)}")
        raise
    except Exception as e:
        log_error(logger, f"数据保存过程中发生异常: {str(e)}")
        raise

def main():
    """主函数"""
    import time

    start_time = time.time()
    log_progress(logger, "=== 股票基本信息更新开始 ===")

    try:
        # 初始化数据库
        init_start = time.time()
        init_database()
        init_time = time.time() - init_start
        log_progress(logger, f"数据库初始化完成,耗时 {init_time:.2f} 秒")

        # 获取股票数据
        fetch_start = time.time()
        stock_df = get_stock_data()
        fetch_time = time.time() - fetch_start
        log_progress(logger, f"数据获取完成,耗时 {fetch_time:.2f} 秒")

        # 保存数据
        save_stock_data(stock_df)

        # 总结
        total_time = time.time() - start_time
        log_progress(logger, f"=== 股票基本信息更新完成 ===")
        log_progress(logger, f"总计处理 {len(stock_df)} 条记录,总耗时 {total_time:.2f} 秒")
        log_progress(logger, f"平均处理速度: {len(stock_df)/total_time:.0f} 条/秒")

        return True

    except Exception as e:
        total_time = time.time() - start_time
        error_msg = str(e)
        log_error(logger, f"程序执行异常 (耗时 {total_time:.2f} 秒): {error_msg}")

        # 保留失败时的通知
        send_notification(
            message=f"股票基本信息更新失败: {error_msg}",
            title="股票基本信息更新异常",
            tags="基本信息|异常"
        )
        return False

if __name__ == '__main__':
    import sys

    success = main()
    if not success:
        sys.exit(1)
