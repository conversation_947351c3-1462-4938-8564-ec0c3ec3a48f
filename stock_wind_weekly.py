#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
WindPy API 周线数据获取脚本
用于获取各类高质量的金融周线数据

使用 w.wsd() 函数获取周线数据,通过设置 options 参数中的 Period=W 来指定周线数据
"""

from WindPy import w
import pandas as pd
from datetime import datetime, timedelta
import sys
import pymysql
from sqlalchemy import create_engine, text
from sqlalchemy.exc import SQLAlchemyError
import os
import time
import warnings
import pathlib
from contextlib import contextmanager
from sqlalchemy.pool import QueuePool


# 创建日志目录
log_dir = pathlib.Path(__file__).parent / 'log'
log_dir.mkdir(exist_ok=True)

# 获取脚本名称
script_name = os.path.splitext(os.path.basename(__file__))[0]

# 数据库连接配置
DB_CONFIG = {
    'host': '*************',
    'port': 13306,
    'user': 'root',
    'password': '31490600',
    'database': 'instockdb',
    'charset': 'utf8mb4'
}

# 处理配置
PROCESS_CONFIG = {
    'show_progress': True,   # 是否显示进度信息
    'max_retries': 3,       # 最大重试次数
    'retry_delay': 5,       # 初始重试延迟(秒)
    'api_interval': 0.01,    # API调用间隔(秒)
    'check_connection': False,  # 是否每次检查API连接
    'connection_wait': 0.001,  # 连接等待时间(秒)

    # --- 数据获取参数配置 ---
    'fields_to_fetch': ["open", "high", "low", "close", "volume"], # 要获取的指标
    'options': "PriceAdj=F;Period=W",    # WSD选项, F:前复权, Period=W:周线数据
    'begin_date': "2022-01-01", # 数据开始日期 
    'end_date': datetime.now().strftime("%Y-%m-%d") # 数据结束日期
}

# 数据库连接池配置
DB_POOL_SIZE = 5
DB_MAX_OVERFLOW = 10
DB_POOL_TIMEOUT = 30

# 全局数据库引擎
_DB_ENGINE = None

# 简单的日志函数
def log_progress(message):
    """记录进度日志"""
    timestamp = datetime.now().strftime('%Y-%m-%d %H:%M:%S')
    print(f"[{timestamp}] {message}")
    with open(log_dir / f"{script_name}.log", 'a', encoding='utf-8') as f:
        f.write(f"[{timestamp}] {message}\n")

def log_error(message):
    """记录错误日志"""
    timestamp = datetime.now().strftime('%Y-%m-%d %H:%M:%S')
    print(f"[{timestamp}] ERROR: {message}")
    with open(log_dir / f"{script_name}.log", 'a', encoding='utf-8') as f:
        f.write(f"[{timestamp}] ERROR: {message}\n")


def start_wind_api(wait_time=120):
    """
    启动WindPy API
    
    参数:
        wait_time (int): 超时时间,默认120秒
        
    返回:
        bool: 启动是否成功
    """
    log_progress("正在启动WindPy API...")
    try:
        # 尝试导入WindPy
        from WindPy import w
        log_progress("WindPy模块导入成功")
        
        # 启动API
        result = w.start(waitTime=wait_time)
        log_progress(f"WindPy启动返回值: {result}")
        
        # WindPy返回值是一个对象,需要检查ErrorCode属性
        if hasattr(result, 'ErrorCode') and result.ErrorCode != 0:
            log_error("WindPy API启动失败")
            log_error("\n可能的解决方案:")
            log_error("1. 确保已安装Wind终端")
            log_error("2. 确保Wind终端正在运行")
            log_error("3. 确保已正确登录Wind终端")
            log_error("4. 确保已安装WindPy插件")
            log_error("5. 尝试增加wait_time参数值")
            return False
        
        if not w.isconnected():
            log_error("WindPy未连接成功")
            return False
            
        log_progress("WindPy API启动成功")
        return True
    except ImportError:
        log_error("错误: 无法导入WindPy模块")
        log_error("\n可能的解决方案:")
        log_error("1. 确保已安装WindPy插件")
        log_error("2. 检查Python环境变量设置")
        log_error("3. 尝试重新安装WindPy插件")
        return False
    except Exception as e:
        log_error(f"WindPy API启动失败,错误信息: {str(e)}")
        log_error("\n可能的解决方案:")
        log_error("1. 重启Wind终端")
        log_error("2. 检查网络连接")
        log_error("3. 联系Wind技术支持")
        return False

def stop_wind_api():
    """停止WindPy API"""
    log_progress("正在停止WindPy API...")
    w.stop()
    log_progress("WindPy API已停止")

def get_db_engine(config=None):
    """
    获取SQLAlchemy数据库引擎(使用连接池)
    
    参数:
        config (dict): 数据库配置,如果为None则使用默认配置
        
    返回:
        Engine: SQLAlchemy引擎对象
    """
    global _DB_ENGINE
    
    if config is None:
        # 使用全局数据库配置
        config = DB_CONFIG.copy()
    
    # 如果已存在引擎且状态正常,直接返回
    if _DB_ENGINE is not None:
        try:
            # 测试连接
            with _DB_ENGINE.connect() as conn:
                conn.execute(text("SELECT 1"))
            return _DB_ENGINE
        except Exception:
            # 连接失败,需要重新创建
            _DB_ENGINE = None
    
    # 创建新的数据库引擎
    try:
        from sqlalchemy.engine.url import URL
        db_url = URL.create(
            drivername="mysql+pymysql",
            username=config['user'],
            password=config['password'],
            host=config['host'],
            port=config['port'],
            database=config['database']
        )
        
        _DB_ENGINE = create_engine(
            db_url,
            poolclass=QueuePool,
            pool_size=DB_POOL_SIZE,
            max_overflow=DB_MAX_OVERFLOW,
            pool_timeout=DB_POOL_TIMEOUT,
            pool_recycle=3600  # 1小时后回收连接
        )
        
        # 测试连接
        with _DB_ENGINE.connect() as conn:
            conn.execute(text("SELECT 1"))
            
        log_progress("MySQL数据库引擎创建成功(使用连接池)")
        return _DB_ENGINE
        
    except Exception as e:
        log_error(f"创建数据库引擎失败: {str(e)}")
        return None

@contextmanager
def db_connection(config=None):
    """
    数据库连接上下文管理器
    
    参数:
        config (dict): 数据库配置,如果为None则使用默认配置
        
    使用方法:
        with db_connection() as conn:
            # 使用连接执行操作
            pass
    """
    engine = get_db_engine(config)
    if engine is None:
        log_error("无法获取数据库引擎")
        yield None
        return
        
    connection = None
    try:
        # 从连接池获取连接
        connection = engine.connect()
        yield connection
    except Exception as e:
        log_error(f"数据库连接失败: {str(e)}")
        yield None
    finally:
        if connection:
            connection.close()

def get_mysql_connection(config=None):
    """
    获取MySQL数据库连接(兼容旧接口)
    
    参数:
        config (dict): 数据库配置,如果为None则使用默认配置
        
    返回:
        connection: 数据库连接对象
    """
    engine = get_db_engine(config)
    if engine is None:
        return None
        
    try:
        # 使用engine.raw_connection()获取原始DBAPI连接
        raw_conn = engine.raw_connection()
        return raw_conn
    except Exception as e:
        log_error(f"MySQL数据库连接失败: {str(e)}")
        return None

class MySQLConnectionWrapper:
    """
    MySQL连接包装器,将SQLAlchemy连接包装为类似pymysql的连接
    """
    def __init__(self, sqlalchemy_conn):
        self._conn = sqlalchemy_conn
        self._cursor = None
        
    def cursor(self):
        """创建游标"""
        if self._cursor is None:
            # 使用SQLAlchemy的engine.raw_connection()获取底层DBAPI连接
            self._cursor = self._conn.engine.raw_connection().cursor()
        return self._cursor
        
    def commit(self):
        """提交事务"""
        self._conn.commit()
        
    def rollback(self):
        """回滚事务"""
        self._conn.rollback()
        
    def close(self):
        """关闭连接"""
        if self._cursor:
            self._cursor.close()
            self._cursor = None
        self._conn.close()
        
    @property
    def user(self):
        return self._conn.engine.url.username
        
    @property
    def password(self):
        return self._conn.engine.url.password
        
    @property
    def host(self):
        return self._conn.engine.url.host
        
    @property
    def port(self):
        return self._conn.engine.url.port
        
    @property
    def db(self):
        return self._conn.engine.url.database
        
    def get_host_info(self):
        """获取主机信息(兼容pymysql接口)"""
        return f"{self.host} via TCP/IP"

def get_stock_codes_from_wind():
    """
    通过Wind API获取所有A股代码
    :return: 包含股票代码和名称的DataFrame
    """
    log_progress("正在通过Wind API获取所有A股代码...")
    # sectorid=a001010100000000 代表全部A股
    today_str = datetime.now().strftime("%Y-%m-%d")
    params = f"date={today_str};sectorid=a001010100000000"
    result = retry_api_call(w.wset, "sectorconstituent", params)

    if result is not None and result.Data and len(result.Data) >= 2:
        # Wind API sectorconstituent 返回的数据结构通常是:
        # Data[0]: 日期列表 (所有记录的日期都相同)
        # Data[1]: 股票代码列表
        # Data[2]: 股票名称列表 (如果有的话)

        # 简化调试信息
        log_progress(f"Wind API返回 {len(result.Data)} 列数据")

        # 根据实际数据结构解析
        if len(result.Data) >= 3:
            # 有3列数据:日期、代码、名称
            dates = result.Data[0]
            codes = result.Data[1]
            names = result.Data[2]

            # 验证数据格式
            if codes and isinstance(codes[0], str) and '.' in codes[0]:
                log_progress("识别到标准格式: [日期, 股票代码, 股票名称]")
                df = pd.DataFrame({'ts_code': codes, 'name': names})
            else:
                log_error("数据格式不符合预期")
                return None

        elif len(result.Data) >= 2:
            # 只有2列数据,需要判断哪列是代码
            data1 = result.Data[0]
            data2 = result.Data[1]

            if data1 and isinstance(data1[0], str) and '.' in data1[0]:
                # data1是股票代码
                log_progress("识别到格式: [股票代码, 其他数据]")
                df = pd.DataFrame({'ts_code': data1, 'name': data2})
            elif data2 and isinstance(data2[0], str) and '.' in data2[0]:
                # data2是股票代码
                log_progress("识别到格式: [其他数据, 股票代码]")
                df = pd.DataFrame({'ts_code': data2, 'name': data1})
            else:
                log_error("无法识别股票代码列")
                return None
        else:
            log_error("Wind API返回的数据列数不足")
            return None

        # 在DataFrame层面进行去重,防止'repeated windcodes'错误
        original_count = len(df)
        df.drop_duplicates(subset=['ts_code'], inplace=True, keep='first')

        if len(df) < original_count:
            log_progress(f"获取到 {original_count} 条记录, 去重后剩余 {len(df)} 条")
        else:
            log_progress(f"获取到 {len(df)} 只股票信息")

        return df
    else:
        log_error("从Wind API获取股票代码失败, API返回的数据为空或格式不正确.")
        if result is not None:
            log_error(f"API返回结果: ErrorCode={getattr(result, 'ErrorCode', 'N/A')}")
            log_error(f"API返回数据: {getattr(result, 'Data', 'N/A')}")
        return None

def get_wind_code_from_ts_code(ts_code):
    """
    将ts_code转换为Wind代码格式
    
    参数:
        ts_code (str): ts代码格式,如'000001.SZ'
        
    返回:
        str: Wind代码格式,如'000001.SZ'(相同格式,但可能需要转换)
    """
    # ts_code和Wind代码格式可能相同,但如果有特殊转换需求可以在这里处理
    return ts_code

def get_friday_of_week(date):
    """
    获取指定日期所在周的周五日期
    
    参数:
        date (datetime): 日期对象
        
    返回:
        datetime: 周五的日期对象
    """
    # 0是周一,1是周二,2是周三,3是周四,4是周五,5是周六,6是周日
    days_to_friday = 4 - date.weekday()
    if days_to_friday < 0:
        days_to_friday += 7
    friday = date + timedelta(days=days_to_friday)
    return friday

def clear_table_data(conn, table_name):
    """
    清空表数据
    
    参数:
        conn: 数据库连接
        table_name: 表名
        
    返回:
        bool: 是否成功
    """
    try:
        # 检查连接类型并执行SQL
        if hasattr(conn, 'execute'):
            # SQLAlchemy连接
            sql = f"TRUNCATE TABLE {table_name}"
            conn.execute(text(sql))
            conn.commit()
        else:
            # 原始DBAPI连接
            cursor = conn.cursor()
            sql = f"TRUNCATE TABLE {table_name}"
            cursor.execute(sql)
            conn.commit()
            cursor.close()
            
        log_progress(f"已清空表 {table_name} 的数据")
        return True
    except Exception as e:
        log_error(f"清空表 {table_name} 数据失败: {str(e)}")
        try:
            if hasattr(conn, 'rollback'):
                conn.rollback()
        except:
            pass
        return False



def retry_api_call(func, *args, max_retries=PROCESS_CONFIG['max_retries'],
                  retry_delay=PROCESS_CONFIG['retry_delay'], **kwargs):
    """
    带重试机制的API调用
    
    参数:
        func: 要调用的函数
        *args: 函数参数
        max_retries: 最大重试次数
        retry_delay: 重试延迟(秒)
        **kwargs: 函数关键字参数
        
    返回:
        函数调用结果或None(如果所有重试都失败)
    """
    last_error = None
    for retry in range(max_retries + 1):
        try:
            result = func(*args, **kwargs)
            
            # 检查结果是否有效
            if result is not None:
                if isinstance(result, tuple) and len(result) > 0 and hasattr(result[0], 'ErrorCode'):
                    if result[0].ErrorCode != 0:
                        raise Exception(f"API返回错误代码: {result[0].ErrorCode}")
                elif hasattr(result, 'ErrorCode') and result.ErrorCode != 0:
                    raise Exception(f"API返回错误代码: {result.ErrorCode}")
                    
                # 成功获取数据
                if retry > 0:
                    log_progress(f"第{retry+1}次重试成功")
                return result
                
        except Exception as e:
            last_error = str(e)
            if retry < max_retries:
                # 指数退避策略
                delay = retry_delay * (2 ** retry)
                log_error(f"第{retry+1}次调用失败: {last_error}, {delay}秒后重试")
                time.sleep(delay)
            else:
                log_error(f"所有重试均失败: {last_error}")
                
    return None

def adjust_to_trading_week_date(date_obj, trade_weeks):
    """
    将日期调整为所在交易周的实际交易周日期

    参数:
        date_obj: 需要调整的日期对象
        trade_weeks: 交易周列表

    返回:
        调整后的日期对象
    """
    if trade_weeks is None or not trade_weeks:
        return date_obj

    # 将输入日期转换为datetime对象
    if isinstance(date_obj, str):
        target_date = pd.to_datetime(date_obj)
    else:
        target_date = pd.to_datetime(date_obj)

    # 查找最接近的交易周日期
    min_diff = float('inf')
    closest_week = None

    for week_date in trade_weeks:
        week_dt = pd.to_datetime(week_date)
        # 计算日期差异(以天为单位)
        diff = abs((target_date - week_dt).days)

        # 如果目标日期在这个交易周的范围内(前后4天内),选择这个交易周
        # 这样可以覆盖一个完整的交易周(周一到周五)
        if diff <= 4 and diff < min_diff:
            min_diff = diff
            closest_week = week_dt

    # 如果找到了合适的交易周,返回交易周日期;否则返回原日期
    return closest_week if closest_week is not None else target_date


def get_friday_of_week(date_obj):
    """
    获取指定日期所在周的周五日期

    参数:
        date_obj: 日期对象

    返回:
        该周的周五日期
    """
    if isinstance(date_obj, str):
        date_obj = pd.to_datetime(date_obj)

    # 获取当前日期是周几 (0=周一, 6=周日)
    weekday = date_obj.weekday()

    # 计算到周五的天数差
    days_to_friday = 4 - weekday  # 4 = 周五

    # 如果已经是周五或之后,获取下周五
    if days_to_friday <= 0:
        days_to_friday += 7

    friday = date_obj + pd.Timedelta(days=days_to_friday)
    return friday


def adjust_weekly_dates_batch(df, trade_weeks):
    """
    批量调整DataFrame中的交易周日期

    参数:
        df: 包含trade_date列的DataFrame
        trade_weeks: 交易周列表

    返回:
        调整后的DataFrame和调整统计信息
    """
    if trade_weeks is None or not trade_weeks or 'trade_date' not in df.columns:
        return df, 0

    # 创建交易周日期映射字典,提高查找效率
    trade_weeks_dt = [pd.to_datetime(week) for week in trade_weeks]

    adjusted_count = 0
    unique_dates = df['trade_date'].unique()
    date_mapping = {}
    current_date = pd.to_datetime('today').normalize()

    # 为每个唯一日期计算调整后的日期
    for original_date in unique_dates:
        target_date = pd.to_datetime(original_date)
        adjusted_date = target_date

        # 特殊处理:如果是当前周且不是周五,调整为周五
        if target_date.date() >= current_date.date():
            # 检查是否是周一到周四
            if target_date.weekday() < 4:  # 0-3 = 周一到周四
                friday_date = get_friday_of_week(target_date)
                # 确保周五不超过当前日期太多(避免未来日期)
                if friday_date <= current_date + pd.Timedelta(days=7):
                    adjusted_date = friday_date
                    log_progress(f"将当前周日期 {target_date.strftime('%Y-%m-%d')} 调整为周五 {adjusted_date.strftime('%Y-%m-%d')}")

        # 如果日期被调整了,记录映射
        if adjusted_date != target_date:
            date_mapping[original_date] = adjusted_date
            adjusted_count += len(df[df['trade_date'] == original_date])

    # 应用日期映射
    if date_mapping:
        df['trade_date'] = df['trade_date'].map(lambda x: date_mapping.get(x, x))

    return df, adjusted_count


def get_weekly_data_by_field(codes, field, begin_date, end_date, options=""):
    """
    获取所有codes在指定时间范围和单个field上的周线数据
    """
    log_progress(f"  获取周线 [{field}] 数据...")

    # 确保options包含周线参数
    if "Period=W" not in options:
        options = f"Period=W;{options}" if options else "Period=W"

    result = retry_api_call(w.wsd, codes, field, begin_date, end_date, options, usedf=True)

    if result is not None:
        df = result[1]

        if not df.empty:
            # 重置索引并转换为长格式
            df_reset = df.reset_index()

            # 重命名列 - 第一列是日期,其余列是股票代码对应的数据
            new_columns = ['trade_date'] + [col for col in df_reset.columns[1:]]
            df_reset.columns = new_columns

            # 转换为长格式
            df_long = pd.melt(df_reset, id_vars=['trade_date'], var_name='ts_code', value_name=field)

            # 过滤掉API可能返回的错误信息行
            df_filtered = df_long[df_long['ts_code'].str.contains('.', na=False)]

            log_progress(f"  获取到 {len(df_filtered)} 条 [{field}] 记录")
            return df_filtered

    log_error(f"获取周线 [{field}] 数据失败")
    return None



def process_all_weekly_stocks():
    """获取所有股票代码的周线数据并写入数据库"""
    log_progress("===== 开始获取所有股票代码的周线数据并写入数据库 =====")

    # 通过Wind API获取股票代码
    stocks_df = get_stock_codes_from_wind()

    if stocks_df is None or len(stocks_df) == 0:
        log_error("无法获取股票代码")
        return

    # 清空表数据
    log_progress("正在清空表 ts_stock_weekly 的数据...")
    with db_connection() as db_conn:
        if db_conn is None:
            log_error("无法连接到数据库,请检查数据库配置")
            return

        if not clear_table_data(db_conn, 'ts_stock_weekly'):
            log_error("清空表数据失败,处理终止")
            return

    all_codes = stocks_df['ts_code'].tolist()
    fields_to_fetch = PROCESS_CONFIG['fields_to_fetch']
    options = PROCESS_CONFIG['options']
    begin_date = PROCESS_CONFIG['begin_date']
    end_date = PROCESS_CONFIG['end_date']

    log_progress(f"数据获取时间范围: {begin_date} 至 {end_date}")
    log_progress(f"获取指标: {fields_to_fetch}")
    log_progress(f"WSD选项: {options}")

    # 获取交易日历 - 用于验证周线数据的完整性
    log_progress("正在获取交易日历...")
    try:
        trade_days_result = retry_api_call(w.tdays, begin_date, end_date, "Period=W")
        if trade_days_result is not None and trade_days_result.Data:
            trade_weeks = trade_days_result.Data[0]
            log_progress(f"获取到 {len(trade_weeks)} 个交易周")

            # 显示交易周范围和详细信息
            if trade_weeks:
                first_week = trade_weeks[0].strftime("%Y-%m-%d") if hasattr(trade_weeks[0], 'strftime') else str(trade_weeks[0])
                last_week = trade_weeks[-1].strftime("%Y-%m-%d") if hasattr(trade_weeks[-1], 'strftime') else str(trade_weeks[-1])
                log_progress(f"交易周范围: {first_week} 至 {last_week}")

                # 显示所有交易周日期,用于调试
                week_dates = [week.strftime("%Y-%m-%d") if hasattr(week, 'strftime') else str(week) for week in trade_weeks]
                log_progress(f"所有交易周日期: {week_dates}")
        else:
            log_progress("获取交易日历失败,将使用指定的日期范围")
            trade_weeks = None
    except Exception as e:
        log_progress(f"获取交易日历时出错: {str(e)},将使用指定的日期范围")
        trade_weeks = None

    # 获取周线数据
    log_progress("正在获取周线数据...")

    all_field_dfs = []
    # 按指标循环
    for field in fields_to_fetch:
        df_field = get_weekly_data_by_field(all_codes, field, begin_date, end_date, options)
        if df_field is not None and not df_field.empty:
            all_field_dfs.append(df_field)
        else:
            log_progress(f"  获取 {field} 数据失败")

    # 合并所有指标的数据
    if len(all_field_dfs) > 0:
        log_progress(f"正在合并 {len(all_field_dfs)} 个指标的数据...")
        from functools import reduce
        merged_df = reduce(lambda left, right: pd.merge(left, right, on=['ts_code', 'trade_date'], how='outer'), all_field_dfs)
        log_progress(f"合并后数据: {merged_df.shape[0]} 条记录")
    else:
        log_progress("没有获取到任何有效数据")
        return

    # 数据处理
    log_progress("正在处理数据...")
    final_df = merged_df.copy()

    # 调整交易日期为准确的交易周日期
    if trade_weeks is not None:
        log_progress("正在调整交易周日期...")

        # 显示调整前的日期情况
        original_dates = sorted(final_df['trade_date'].unique())
        log_progress(f"调整前的日期: {[date.strftime('%Y-%m-%d') if hasattr(date, 'strftime') else str(date) for date in original_dates]}")

        final_df, adjusted_count = adjust_weekly_dates_batch(final_df, trade_weeks)

        if adjusted_count > 0:
            log_progress(f"调整了 {adjusted_count} 条记录的交易周日期")

            # 显示调整后的日期情况
            adjusted_dates = sorted([pd.to_datetime(date) for date in final_df['trade_date'].unique()])
            log_progress(f"调整后的日期: {[date.strftime('%Y-%m-%d') if hasattr(date, 'strftime') else str(date) for date in adjusted_dates]}")
        else:
            log_progress("所有日期都已是正确的交易周日期")

    # 创建字段名映射,将Wind API字段名映射到数据库列名
    field_mapping = {
        'open': 'open',
        'high': 'high',
        'low': 'low',
        'close': 'close',
        'volume': 'vol'  # volume -> vol
    }

    # 重命名列
    rename_dict = {field: field_mapping.get(field, field) for field in fields_to_fetch}
    final_df.rename(columns=rename_dict, inplace=True)

    # 补充股票信息
    stock_info_map = pd.Series(stocks_df.name.values, index=stocks_df.ts_code).to_dict()
    final_df['name'] = final_df['ts_code'].map(stock_info_map)
    final_df['stock_code'] = final_df['ts_code'].str.split('.').str[0]

    # 整理数据库列 - 只包含周线表实际存在的字段
    db_columns = [
        'ts_code', 'stock_code', 'name', 'trade_date', 'open', 'high', 'low',
        'close', 'vol'
    ]

    for col in db_columns:
        if col not in final_df.columns:
            final_df[col] = None

    final_df = final_df[db_columns]

    # 过滤空数据
    before_filter = len(final_df)
    final_df.dropna(subset=['open', 'high', 'low', 'close', 'vol'], how='all', inplace=True)
    log_progress(f"数据过滤: {before_filter} -> {len(final_df)} 条记录")

    if final_df.empty:
        log_progress("最终数据为空!")
        return

    # 数据类型转换
    log_progress("正在转换数据类型...")

    # 确保数值列为float类型
    numeric_columns = ['open', 'high', 'low', 'close', 'vol', 'pre_close', 'change', 'pct_chg', 'amount', 'adj_factor']
    for col in numeric_columns:
        if col in final_df.columns:
            final_df[col] = pd.to_numeric(final_df[col], errors='coerce')

    # 确保日期列为datetime类型
    final_df['trade_date'] = pd.to_datetime(final_df['trade_date'], errors='coerce')

    # 确保字符串列为string类型
    string_columns = ['ts_code', 'stock_code', 'name']
    for col in string_columns:
        if col in final_df.columns:
            final_df[col] = final_df[col].astype(str)

    # 保存到数据库
    if not final_df.empty:
        try:
            log_progress("正在保存到数据库...")
            # 使用引擎直接保存,避免连接管理问题
            engine = get_db_engine()
            if engine is None:
                log_error("无法获取数据库引擎")
                return

            # 分批保存,避免内存问题
            batch_size = 10000
            total_rows = len(final_df)
            total_batches = (total_rows - 1) // batch_size + 1

            for i in range(0, total_rows, batch_size):
                batch_df = final_df.iloc[i:i+batch_size].copy()

                batch_df.to_sql(
                    'ts_stock_weekly',
                    engine,
                    if_exists='append',
                    index=False,
                    chunksize=5000
                )

                if total_batches > 1:
                    log_progress(f"已保存批次 {i//batch_size + 1}/{total_batches}")

            log_progress(f"成功保存 {len(final_df)} 条记录到数据库")
        except Exception as e:
            log_error(f"保存数据失败: {str(e)}")
            return
    else:
        log_progress("没有数据需要保存")

    log_progress("===== 处理完成 =====")
    log_progress(f"总共处理了 {len(stocks_df)} 只股票")
    log_progress(f"成功保存了 {len(final_df)} 条记录到数据库")


if __name__ == "__main__":
    # 启动WindPy API
    if start_wind_api():
        try:
            # 处理所有股票周线数据
            process_all_weekly_stocks()

        finally:
            # 停止WindPy API
            stop_wind_api()