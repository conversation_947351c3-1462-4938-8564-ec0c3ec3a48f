import pandas as pd
from sqlalchemy import create_engine, text, pool
from contextlib import contextmanager
import adata
import time
from tqdm import tqdm
from concurrent.futures import ThreadPoolExecutor, as_completed
import os
from dotenv import load_dotenv
import threading
import numpy as np
from datetime import datetime
from functools import lru_cache, wraps
from typing import List, Dict, Any, Optional
import gc  # Add garbage collection
import tushare as ts
import configparser
from log_config import setup_logger, log_progress, log_error, log_warning, send_notification

# 获取脚本名称
script_name = os.path.splitext(os.path.basename(__file__))[0]

# 配置日志
logger = setup_logger(script_name)

def load_config():
    """加载配置文件"""
    config = configparser.ConfigParser()
    config_path = os.path.join(os.path.dirname(__file__), 'config.ini')

    if not os.path.exists(config_path):
        log_error(logger, f"配置文件不存在: {config_path}")
        raise FileNotFoundError(f"配置文件不存在: {config_path}")

    try:
        config.read(config_path, encoding='utf-8')
        log_progress(logger, f"成功加载配置文件: {config_path}")
        return config
    except Exception as e:
        log_error(logger, f"加载配置文件失败: {str(e)}")
        raise

def get_config_value(section, key, fallback=None, value_type='str'):
    """
    获取配置值，支持类型转换

    Args:
        section: 配置节
        key: 配置键
        fallback: 默认值
        value_type: 值类型 ('str', 'int', 'float', 'bool')

    Returns:
        配置值
    """
    try:
        if value_type == 'int':
            return config.getint(section, key, fallback=fallback)
        elif value_type == 'float':
            return config.getfloat(section, key, fallback=fallback)
        elif value_type == 'bool':
            return config.getboolean(section, key, fallback=fallback)
        else:
            return config.get(section, key, fallback=fallback)
    except Exception as e:
        log_warning(logger, f"获取配置值失败 {section}.{key}: {str(e)}，使用默认值 {fallback}")
        return fallback

# 加载配置
config = load_config()

def validate_configuration():
    """验证配置完整性"""
    required_configs = [
        ('database', 'host'),
        ('database', 'port'),
        ('database', 'database'),
        ('tushare', 'token')
    ]

    missing_configs = []
    for section, key in required_configs:
        try:
            value = config.get(section, key)
            if not value or value.strip() == '':
                missing_configs.append(f"{section}.{key}")
        except Exception:
            missing_configs.append(f"{section}.{key}")

    if missing_configs:
        error_msg = f"缺少必需的配置项: {', '.join(missing_configs)}"
        log_error(logger, error_msg)
        raise ValueError(error_msg)

    log_progress(logger, "配置验证通过")
    return True

# =============================================================================
# 脚本特定配置 - 财务指标数据处理配置
# =============================================================================
# 这些配置专门用于当前脚本，修改这些常量即可调整脚本行为
# 无需修改其他代码或配置文件

# 财务指标处理配置
MAX_WORKERS = 100  # 最大工作线程数，影响并发处理能力
MAX_CONCURRENT_REQUESTS = 30  # 最大并发请求数，控制API访问频率
CHUNK_SIZE = 150  # 批处理大小，影响内存使用和处理效率
REQUEST_INTERVAL = 0.02  # 请求间隔时间(秒)，避免API限流
RETRY_TIMES = 3  # 重试次数
RETRY_INTERVAL = 0.3  # 重试间隔时间(秒)
DB_BATCH_SIZE = 3000  # 数据库批量写入大小，影响写入性能

# =============================================================================
# 脚本配置参数类 - 从配置文件读取的配置
# =============================================================================
# 这些配置从config.ini文件读取，主要是数据库连接等共享配置

class Config:
    # 数据库配置 - 从配置文件读取
    DB_URL = None  # 不再使用硬编码的默认值

    # 财务指标处理配置 - 使用脚本开头的常量
    MAX_WORKERS = MAX_WORKERS  # 最大线程池大小
    MAX_CONCURRENT_REQUESTS = MAX_CONCURRENT_REQUESTS  # 最大并发请求数
    CHUNK_SIZE = CHUNK_SIZE  # 批处理大小
    REQUEST_INTERVAL = REQUEST_INTERVAL  # 请求间隔
    RETRY_TIMES = RETRY_TIMES  # 重试次数
    RETRY_INTERVAL = RETRY_INTERVAL  # 重试间隔

    # 数据库批处理配置
    DB_BATCH_SIZE = DB_BATCH_SIZE  # 批量写入大小
    

    @classmethod
    def get_db_url(cls):
        # 优先级: 环境变量 > 配置文件 > 默认值
        db_url = os.getenv('DATABASE_URL')
        if db_url:
            return db_url

        # 从配置文件构建数据库URL
        try:
            user = get_config_value('database', 'user', 'root')
            password = get_config_value('database', 'password', '31490600')
            host = get_config_value('database', 'host', '**********')
            port = get_config_value('database', 'port', '3306')
            database = get_config_value('database', 'database', 'instockdb')
            return f"mysql+pymysql://{user}:{password}@{host}:{port}/{database}"
        except Exception:
            return cls.DB_URL
    
    @classmethod
    def get_thread_count(cls, total_stocks: int) -> int:
        cpu_count = os.cpu_count() or 4
        # IO密集型任务，使用适中的线程数
        optimal_threads = min(
            cls.MAX_WORKERS,
            max(cpu_count * 6, (total_stocks + cls.CHUNK_SIZE - 1) // cls.CHUNK_SIZE),
            total_stocks  # 不超过股票总数
        )
        return optimal_threads
    
    @classmethod
    def get_chunk_size(cls, total_stocks: int, thread_count: int) -> int:
        return min(cls.CHUNK_SIZE, (total_stocks + thread_count - 1) // thread_count)

# 字段元数据定义 - 使用元组代替集合,提高查找效率 [citation:7]
STR_COLUMNS = ('stock_code', 'short_name', 'report_type', 'industry_name')
DATE_COLUMNS = ('report_date', 'notice_date')
FLOAT_COLUMNS = (
    'basic_eps', 'diluted_eps', 'non_gaap_eps', 'net_asset_ps',
    'cap_reserve_ps', 'undist_profit_ps', 'oper_cf_ps', 'total_rev',
    'gross_profit', 'net_profit_attr_sh', 'non_gaap_net_profit',
    'total_rev_yoy_gr', 'net_profit_yoy_gr', 'non_gaap_net_profit_yoy_gr',
    'total_rev_qoq_gr', 'net_profit_qoq_gr', 'non_gaap_net_profit_qoq_gr',
    'roe_wtd', 'roe_non_gaap_wtd', 'roa_wtd', 'gross_margin', 'net_margin',
    'adv_receipts_to_rev', 'net_cf_sales_to_rev', 'oper_cf_to_rev',
    'eff_tax_rate', 'curr_ratio', 'quick_ratio', 'cash_flow_ratio',
    'asset_liab_ratio', 'equity_multiplier', 'equity_ratio',
    'total_asset_turn_days', 'inv_turn_days', 'acct_recv_turn_days',
    'total_asset_turn_rate', 'inv_turn_rate', 'acct_recv_turn_rate'
)
# 定义导出Excel时使用的表头
EXCEL_HEADERS = {
    'stock_code': '股票代码',
    'short_name': '股票简称',
    'industry_name': '行业简称',  # 重命名并放在股票简称后面
    'report_date': '报告日期',
    'report_type': '报告类型',
    'notice_date': '公布日期',
    'basic_eps': '基本每股收益(元)',
    'diluted_eps': '稀释每股收益(元)',
    'non_gaap_eps': '扣非每股收益(元)',
    'net_asset_ps': '每股净资产(元)',
    'cap_reserve_ps': '每股公积金(元)',
    'undist_profit_ps': '每股未分配利润(元)',
    'oper_cf_ps': '每股经营现金流(元)',
    'total_rev': '营业总收入(元)',
    'gross_profit': '毛利润(元)',
    'net_profit_attr_sh': '归属净利润(元)',
    'non_gaap_net_profit': '扣非净利润(元)',
    'total_rev_yoy_gr': '营业总收入同比增长(%)',
    'net_profit_yoy_gr': '归属净利润同比增长(%)',
    'non_gaap_net_profit_yoy_gr': '扣非净利润同比增长(%)',
    'total_rev_qoq_gr': '营业总收入环比增长(%)',
    'net_profit_qoq_gr': '归属净利润环比增长(%)',
    'non_gaap_net_profit_qoq_gr': '扣非净利润环比增长(%)',
    'roe_wtd': '净资产收益率(加权)(%)',
    'roe_non_gaap_wtd': '净资产收益率(扣非/加权)(%)',
    'roa_wtd': '总资产收益率(加权)(%)',
    'gross_margin': '毛利率(%)',
    'net_margin': '净利率(%)',
    'adv_receipts_to_rev': '预收账款/营业总收入',
    'net_cf_sales_to_rev': '销售净现金流/营业总收入',
    'oper_cf_to_rev': '经营净现金流/营业总收入',
    'eff_tax_rate': '实际税率(%)',
    'curr_ratio': '流动比率',
    'quick_ratio': '速动比率',
    'cash_flow_ratio': '现金流量比率',
    'asset_liab_ratio': '资产负债率(%)',
    'equity_multiplier': '权益系数',
    'equity_ratio': '产权比率',
    'total_asset_turn_days': '总资产周转天数(天)',
    'inv_turn_days': '存货周转天数(天)',
    'acct_recv_turn_days': '应收账款周转天数(天)',
    'total_asset_turn_rate': '总资产周转率(次)',
    'inv_turn_rate': '存货周转率(次)',
    'acct_recv_turn_rate': '应收账款周转率(次)'
}

# 创建全局数据库连接池
def create_pooled_engine(db_url):
    """创建带连接池的数据库引擎"""
    return create_engine(
        db_url,
        pool_size=20,        # 适中的连接池大小
        max_overflow=30,     # 适中的最大溢出连接数
        pool_recycle=3600,   # 连接回收时间(秒)
        pool_pre_ping=True,  # 自动检测连接是否可用
        pool_timeout=30      # 连接超时时间
    )

@contextmanager
def get_db_connection(db_url=None):
    """创建数据库连接的上下文管理器"""
    global _DB_ENGINE
    if '_DB_ENGINE' not in globals():
        _DB_ENGINE = create_pooled_engine(db_url or Config.get_db_url())
    
    try:
        yield _DB_ENGINE
    except:
        # 如果发生错误,重新创建引擎
        _DB_ENGINE.dispose()
        _DB_ENGINE = create_pooled_engine(db_url or Config.get_db_url())
        raise

# 优化:修改装饰器实现,减少函数调用层级 [citation:6]
def retry_with_semaphore(max_retries=3, wait_time=0.2):
    def decorator(func):
        @wraps(func)
        def wrapper(*args, **kwargs):
            retries = 0
            last_exception = None
            while retries < max_retries:
                try:
                    return func(*args, **kwargs)
                except Exception as e:
                    retries += 1
                    last_exception = e
                    if retries < max_retries:
                        time.sleep(wait_time)
            log_error(logger, f"执行失败,已重试 {max_retries} 次: {str(last_exception)}")
            raise last_exception
        return wrapper
    return decorator

# 优化数据清洗:使用向量化操作 [citation:3][citation:7]
class DataCleaner:
    @staticmethod
    def clean_dataframe(df: pd.DataFrame) -> pd.DataFrame:
        """使用向量化操作高效清洗数据"""
        if df.empty:
            return df
            
        # 处理字符串列 - 向量化操作
        for col in set(STR_COLUMNS) & set(df.columns):
            if df[col].dtype != 'category':  # 只在需要时转换
                df[col] = df[col].fillna('-').astype('category')
            
        # 处理日期列 - 预先转换好格式
        date_format = '%Y-%m-%d'
        for col in set(DATE_COLUMNS) & set(df.columns):
            df[col] = pd.to_datetime(df[col], errors='coerce').dt.strftime(date_format)
            
        # 处理数值列 - 批量转换
        numeric_cols = list(set(FLOAT_COLUMNS) & set(df.columns))
        if numeric_cols:
            df[numeric_cols] = df[numeric_cols].apply(pd.to_numeric, errors='coerce')
            # 替换无效值
            df[numeric_cols] = df[numeric_cols].replace([np.inf, -np.inf], np.nan)
            
        return df

# 添加建表SQL常量(保持不变)
CREATE_TABLE_SQL = """
CREATE TABLE IF NOT EXISTS stock_core_finance (
    stock_code VARCHAR(10) NOT NULL COMMENT '股票代码',
    short_name VARCHAR(50) COMMENT '股票简称',
    report_date DATE NOT NULL COMMENT '报告日期',
    report_type VARCHAR(20) COMMENT '报告类型',
    notice_date DATE COMMENT '公布日期',
    basic_eps DECIMAL(20,4) COMMENT '基本每股收益(元)',
    diluted_eps DECIMAL(20,4) COMMENT '稀释每股收益(元)',
    non_gaap_eps DECIMAL(20,4) COMMENT '扣非每股收益(元)',
    net_asset_ps DECIMAL(20,4) COMMENT '每股净资产(元)',
    cap_reserve_ps DECIMAL(20,4) COMMENT '每股公积金(元)',
    undist_profit_ps DECIMAL(20,4) COMMENT '每股未分配利润(元)',
    oper_cf_ps DECIMAL(20,4) COMMENT '每股经营现金流(元)',
    total_rev DECIMAL(20,4) COMMENT '营业总收入(元)',
    gross_profit DECIMAL(20,4) COMMENT '毛利润(元)',
    net_profit_attr_sh DECIMAL(20,4) COMMENT '归属净利润(元)',
    non_gaap_net_profit DECIMAL(20,4) COMMENT '扣非净利润(元)',
    total_rev_yoy_gr DECIMAL(20,4) COMMENT '营业总收入同比增长(%)',
    net_profit_yoy_gr DECIMAL(20,4) COMMENT '归属净利润同比增长(%)',
    non_gaap_net_profit_yoy_gr DECIMAL(20,4) COMMENT '扣非净利润同比增长(%)',
    total_rev_qoq_gr DECIMAL(20,4) COMMENT '营业总收入滚动环比增长(%)',
    net_profit_qoq_gr DECIMAL(20,4) COMMENT '归属净利润滚动环比增长(%)',
    non_gaap_net_profit_qoq_gr DECIMAL(20,4) COMMENT '扣非净利润滚动环比增长(%)',
    roe_wtd DECIMAL(20,4) COMMENT '净资产收益率(加权)(%)',
    roe_non_gaap_wtd DECIMAL(20,4) COMMENT '净资产收益率(扣非/加权)(%)',
    roa_wtd DECIMAL(20,4) COMMENT '总资产收益率(加权)(%)',
    gross_margin DECIMAL(20,4) COMMENT '毛利率(%)',
    net_margin DECIMAL(20,4) COMMENT '净利率(%)',
    adv_receipts_to_rev DECIMAL(20,4) COMMENT '预收账款/营业总收入',
    net_cf_sales_to_rev DECIMAL(20,4) COMMENT '销售净现金流/营业总收入',
    oper_cf_to_rev DECIMAL(20,4) COMMENT '经营净现金流/营业总收入',
    eff_tax_rate DECIMAL(20,4) COMMENT '实际税率(%)',
    curr_ratio DECIMAL(20,4) COMMENT '流动比率',
    quick_ratio DECIMAL(20,4) COMMENT '速动比率',
    cash_flow_ratio DECIMAL(20,4) COMMENT '现金流量比率',
    asset_liab_ratio DECIMAL(20,4) COMMENT '资产负债率(%)',
    equity_multiplier DECIMAL(20,4) COMMENT '权益系数',
    equity_ratio DECIMAL(20,4) COMMENT '产权比率',
    total_asset_turn_days DECIMAL(20,4) COMMENT '总资产周转天数(天)',
    inv_turn_days DECIMAL(20,4) COMMENT '存货周转天数(天)',
    acct_recv_turn_days DECIMAL(20,4) COMMENT '应收账款周转天数(天)',
    total_asset_turn_rate DECIMAL(20,4) COMMENT '总资产周转率(次)',
    inv_turn_rate DECIMAL(20,4) COMMENT '存货周转率(次)',
    acct_recv_turn_rate DECIMAL(20,4) COMMENT '应收账款周转率(次)',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    PRIMARY KEY (stock_code, report_date)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='股票核心财务指标数据';
"""

# EXCEL_HEADERS 保持不变

# 优化:分批处理数据库插入,提高内存利用率 [citation:1][citation:2]
class StockFinanceProcessor:
    def __init__(self):
        self.semaphore = threading.Semaphore(Config.MAX_CONCURRENT_REQUESTS)
        self.data_cleaner = DataCleaner()
        self.lock = threading.Lock()
        self.processed_count = 0
        self.batch_data = []
        self.db_engine = create_pooled_engine(Config.get_db_url())
        
    @lru_cache(maxsize=5000)  # 增加缓存大小
    def get_stock_info(self, stock_code: str) -> Dict[str, Any]:
        """获取股票基本信息(使用缓存)"""
        try:
            return adata.stock.info.get_industry_sw(stock_code=stock_code) or {}
        except:
            return {}
    
    @retry_with_semaphore(max_retries=3, wait_time=0.2)
    def get_core_finance_data(self, stock_code: str, stock_name: str = None) -> pd.DataFrame:
        """获取股票的核心财务指标数据"""
        try:
            with self.semaphore:
                df = adata.stock.finance.get_core_index(stock_code=stock_code)
                
                if df is None or df.empty:
                    return pd.DataFrame()
                
                # 使用向量化操作预处理数据 [citation:7]
                df = df.drop_duplicates(subset=['stock_code', 'report_date'], keep='first')
                df['report_date'] = pd.to_datetime(df['report_date'], errors='coerce')
                
                # 使用布尔索引过滤数据,避免多次循环 [citation:3]
                mask = (df['report_date'].dt.year >= 2021) & (df['report_type'] == '年报')
                df = df[mask]
                
                if df.empty:
                    return pd.DataFrame()
                
                # 获取最新股票简称
                latest_short_name = (
                    df['short_name'].iloc[0] if not df.empty
                    else stock_name or '-'
                )
                
                # 添加空的年报记录
                current_year = datetime.now().year
                last_year_report_date = f"{current_year-1}-12-31"

                if not any(df['report_date'].dt.strftime('%Y-%m-%d') == last_year_report_date):
                    # 预创建空记录,避免多次字典操作 [citation:6]
                    empty_data = {
                        'stock_code': stock_code,
                        'short_name': latest_short_name,
                        'report_date': last_year_report_date,
                        'report_type': '年报',
                        'notice_date': None
                    }

                    # 一次性为所有FLOAT_COLUMNS设置空值
                    for col in FLOAT_COLUMNS:
                        empty_data[col] = None

                    # 创建空DataFrame并过滤掉空行后合并，避免FutureWarning
                    empty_df = pd.DataFrame([empty_data])
                    if not empty_df.empty:
                        df = pd.concat([df, empty_df], ignore_index=True)
                
                # 清理数据
                return self.data_cleaner.clean_dataframe(df)
                
        except Exception as e:
            logger.error(f"处理股票 {stock_code} 失败: {str(e)}")
            return pd.DataFrame()
    
    def process_stock_batch(self, batch: List[str]) -> None:
        """处理一批股票数据并直接保存到批处理队列"""
        batch_data = []
        failed_stocks = []
        
        for stock_code in batch:
            retry_count = 0
            while retry_count < Config.RETRY_TIMES:
                try:
                    df = self.get_core_finance_data(stock_code)
                    if df is not None and not df.empty:
                        batch_data.append(df)
                    # 减少每个请求的等待时间
                    time.sleep(Config.REQUEST_INTERVAL)
                    break  # 成功则跳出重试循环
                except Exception as e:
                    retry_count += 1
                    if retry_count == Config.RETRY_TIMES:
                        log_error(logger, f"处理股票 {stock_code} 失败 (已重试{retry_count}次): {str(e)}")
                        failed_stocks.append(stock_code)
                    else:
                        log_warning(logger, f"处理股票 {stock_code} 失败,正在进行第{retry_count}次重试: {str(e)}")
                        time.sleep(Config.RETRY_INTERVAL)
        
        # 合并批次数据
        if batch_data:
            try:
                combined_df = pd.concat(batch_data, ignore_index=True)
                
                # 使用锁保护共享资源
                with self.lock:
                    self.batch_data.append(combined_df)
                    self.processed_count += len(batch) - len(failed_stocks)
                    
                    # 当累积的数据足够多时,立即保存到数据库
                    if sum(len(df) for df in self.batch_data) >= Config.DB_BATCH_SIZE:
                        retry_count = 0
                        while retry_count < Config.RETRY_TIMES:
                            try:
                                self._save_batch_to_db()
                                break
                            except Exception as e:
                                retry_count += 1
                                if retry_count == Config.RETRY_TIMES:
                                    log_error(logger, f"保存批次数据到数据库失败 (已重试{retry_count}次): {str(e)}")
                                    raise
                                else:
                                    log_warning(logger, f"保存批次数据到数据库失败,正在进行第{retry_count}次重试: {str(e)}")
                                    time.sleep(Config.RETRY_INTERVAL)
                    
            except Exception as e:
                log_error(logger, f"合并批次数据失败: {str(e)}")
                # 记录失败的股票
                failed_stocks.extend([df['stock_code'].iloc[0] for df in batch_data])
                
        # 如果有失败的股票,记录到日志
        if failed_stocks:
            log_error(logger, f"本批次中以下股票处理失败: {', '.join(failed_stocks)}")
        
        # 帮助垃圾收集
        batch_data = None
        gc.collect()
    
    def _save_batch_to_db(self):
        """将当前批次数据保存到数据库"""
        if not self.batch_data:
            return
            
        retry_count = 0
        while retry_count < Config.RETRY_TIMES:
            try:
                # 合并所有DataFrame
                df = pd.concat(self.batch_data, ignore_index=True)
                if df.empty:
                    return
                    
                # 清理数据并准备入库
                # 排除industry_name字段
                columns = [col for col in df.columns if col != 'industry_name']
                insert_df = df[columns]
                
                # 批量插入
                insert_df.to_sql(
                    name='stock_core_finance',
                    con=self.db_engine,
                    if_exists='append',
                    index=False,
                    method='multi',
                    chunksize=2000
                )
                log_progress(logger, f"成功批量插入 {len(insert_df)} 条记录")
                
                # 清空批次数据
                self.batch_data = []
                gc.collect()
                break
                
            except Exception as e:
                retry_count += 1
                if retry_count == Config.RETRY_TIMES:
                    log_error(logger, f"数据库写入失败 (已重试{retry_count}次): {str(e)}")
                    raise
                else:
                    log_warning(logger, f"数据库写入失败,正在进行第{retry_count}次重试: {str(e)}")
                    time.sleep(Config.RETRY_INTERVAL)

    def flush_remaining_data(self):
        """保存所有剩余的数据"""
        with self.lock:
            if self.batch_data:
                retry_count = 0
                while retry_count < Config.RETRY_TIMES:
                    try:
                        self._save_batch_to_db()
                        break
                    except Exception as e:
                        retry_count += 1
                        if retry_count == Config.RETRY_TIMES:
                            log_error(logger, f"保存剩余数据失败 (已重试{retry_count}次): {str(e)}")
                            raise
                        else:
                            log_warning(logger, f"保存剩余数据失败,正在进行第{retry_count}次重试: {str(e)}")
                            time.sleep(Config.RETRY_INTERVAL)

def main():
    # 忽略SSL警告
    import warnings
    warnings.filterwarnings('ignore', category=Warning)

    # 加载环境变量
    load_dotenv()

    # 验证配置
    validate_configuration()

    # 输出配置信息
    log_progress(logger, f"财务指标处理配置:")
    log_progress(logger, f"  最大工作线程数: {Config.MAX_WORKERS}")
    log_progress(logger, f"  最大并发请求数: {Config.MAX_CONCURRENT_REQUESTS}")
    log_progress(logger, f"  批处理大小: {Config.CHUNK_SIZE}")
    log_progress(logger, f"  请求间隔: {Config.REQUEST_INTERVAL}秒")
    log_progress(logger, f"  重试次数: {Config.RETRY_TIMES}")
    log_progress(logger, f"  数据库批处理大小: {Config.DB_BATCH_SIZE}")

    start_time = time.time()
    
    try:
        processor = StockFinanceProcessor()
        
        # 创建表 (如果不存在)
        with processor.db_engine.begin() as conn:
            conn.execute(text(CREATE_TABLE_SQL))
        
        # 清空表
        with processor.db_engine.begin() as conn:
            conn.execute(text('TRUNCATE TABLE stock_core_finance'))
            log_progress(logger, "表已清空,开始收集数据...")
        
        # 使用tushare获取所有股票代码
        tushare_token = os.getenv('TUSHARE_TOKEN', get_config_value('tushare', 'token', '5aba669a485730beded0d604bd8ccfdaca39f8c9c09ef2d0ed78ed77'))
        pro = ts.pro_api(tushare_token)
        
        # 获取所有上市公司列表 - 修复fields参数类型
        stock_list = pro.stock_basic(exchange='', list_status='L', 
                                   fields='symbol,name')  # 修改为字符串形式
        stock_codes = stock_list['symbol'].tolist()
        
        total_stocks = len(stock_codes)
        log_progress(logger, f"获取到 {total_stocks} 只股票,开始处理...")
        
        # 计算并发参数
        thread_count = Config.get_thread_count(total_stocks)
        chunk_size = Config.get_chunk_size(total_stocks, thread_count)
        
        log_progress(logger, f"并发配置: 线程数={thread_count}, 批大小={chunk_size}, 最大并发请求数={Config.MAX_CONCURRENT_REQUESTS}")
        
        # 分割批次
        batches = [
            stock_codes[i:i + chunk_size]
            for i in range(0, len(stock_codes), chunk_size)
        ]
        
        # 使用线程池处理数据并直接写入数据库
        with ThreadPoolExecutor(max_workers=thread_count) as executor:
            futures = [
                executor.submit(processor.process_stock_batch, batch)
                for batch in batches
            ]
            
            with tqdm(total=len(futures), desc="处理进度", mininterval=0.5) as pbar:
                for future in as_completed(futures):
                    try:
                        future.result()
                    except Exception as e:
                        log_error(logger, f"处理批次时发生错误: {str(e)}")
                    pbar.update(1)
        
        # 保存所有剩余的数据
        processor.flush_remaining_data()
            
        elapsed_time = time.time() - start_time
        log_progress(logger, f"所有处理完成,总耗时: {elapsed_time:.2f}秒,共处理 {total_stocks} 只股票")
        
    except Exception as e:
        error_msg = str(e)
        log_error(logger, f"处理失败: {error_msg}")
        # 仅在失败时发送通知
        send_notification(
            message=f"错误详情: {error_msg}",
            title="财务指标数据处理失败",
            tags="财务指标|异常"
        )
        raise

if __name__ == '__main__':
    main()
