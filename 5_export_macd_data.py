import pandas as pd
from sqlalchemy import create_engine, text
from contextlib import contextmanager
from datetime import datetime, timedelta
import os
import sys
import configparser
import time

# 添加项目根目录到路径,以便导入 trade_calendar
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
from trade_calendar import is_trade_day
from log_config import setup_logger, log_progress, log_error, log_warning, send_notification

# 获取脚本名称
script_name = os.path.splitext(os.path.basename(__file__))[0]

# 配置日志系统
logger = setup_logger(script_name)

# 全局配置对象
_config = None

def get_config():
    """获取配置信息（单例模式）"""
    global _config
    if _config is None:
        _config = configparser.ConfigParser()
        config_path = os.path.join(os.path.dirname(__file__), 'config.ini')
        if not os.path.exists(config_path):
            raise FileNotFoundError(f"配置文件 {config_path} 不存在")
        _config.read(config_path, encoding='utf-8')
    return _config

def get_db_config():
    """获取数据库配置"""
    config = get_config()
    return {
        'host': config.get('database', 'host'),
        'port': config.get('database', 'port'),
        'username': config.get('database', 'username'),
        'password': config.get('database', 'password'),
        'database': config.get('database', 'database')
    }

def get_db_pool_config():
    """获取数据库连接池配置"""
    config = get_config()
    return {
        'pool_size': config.getint('database.pool', 'pool_size', fallback=10),
        'max_overflow': config.getint('database.pool', 'max_overflow', fallback=20),
        'pool_timeout': config.getint('database.pool', 'pool_timeout', fallback=30),
        'pool_recycle': config.getint('database.pool', 'pool_recycle', fallback=3600),
        'pool_pre_ping': config.getboolean('database.pool', 'pool_pre_ping', fallback=True),
        'echo': config.getboolean('database.pool', 'echo', fallback=False)
    }

def get_export_config():
    """获取导出配置"""
    config = get_config()
    return {
        'local_dir': config.get('export', 'local_dir', fallback='/Users/<USER>/Documents/git/stock-analysis-engine/股票财务指标数据'),
        'server_dir': config.get('export', 'server_dir', fallback='/home/<USER>/jupyter/stock_data')
    }

# 定义列名映射
COLUMN_MAPPING = {
    'date': '交易日期',
    'code': '证券代码',
    'name': '证券简称',
    'close': '当天收盘价',
    'diff_1': 'DIFF_1低',
    'diff_2': 'DIFF_2低',
    'diff1_date': '日期-1低',
    'diff2_date': '日期-2低',
    'diff1_price': '收盘价-1低',
    'diff2_price': '收盘价-2低',
    'jd': '背离判断',
    'd_td': '距离d天数',
    'ma_26d': '周线趋向',
    'diff_26d': 'DIFF趋向',
    'huitiao': '回调',
    'macd_diff1': '26D_DIFF1',
    'macd_diff2': '26D_DIFF2',
    'dmd': '现值和最近低值涨幅',
    'diff10_min': 'DIFF_10_MIN',
    'diff10_date': 'DIFF10_DATE',
    'df_10_td': 'DF_10_TD',
    'ma100': 'ma100',
    'atr': 'atr',
    'atrma5': 'atrma5',
    'slope': 'slope',
    'slope-1': 'slope-1',
    'power_slope': 'power_slope',
    'r2_slope': 'r2_slope',
    'slope90': 'slope90',
    'trix_26': 'trix_26',
    'trix_diff': 'trix_26与上一日差值',
    'trix': 'trix_66',
    'trix_26_diff': 'trix_66与上一日差值',
    'ema_diff_smooth66': 'EMA(13-26)的66天0阶平滑',
    'diff0_price': '保持diff值不变所需的下一日收盘价'
}

# 定义列的顺序
COLUMN_ORDER = [
    '交易日期', '证券代码', '证券简称', '当天收盘价', 'DIFF_1低', 'DIFF_2低',
    '日期-1低', '日期-2低', '收盘价-1低', '收盘价-2低', '背离判断', '距离d天数',
    '周线趋向', 'DIFF趋向', '回调', '26D_DIFF1', '26D_DIFF2', '现值和最近低值涨幅',
    'DIFF_10_MIN', 'DIFF10_DATE', 'DF_10_TD', 'ma100', 'atr', 'atrma5',
    'slope', 'slope-1', 'power_slope', 'r2_slope', 'slope90',
    'trix_26', 'trix_26与上一日差值', 'trix_66', 'trix_66与上一日差值',
    'EMA(13-26)的66天0阶平滑', '保持diff值不变所需的下一日收盘价'
]

# 全局数据库引擎
_DB_ENGINE = None

def get_db_engine():
    """获取数据库引擎（单例模式）"""
    global _DB_ENGINE
    if _DB_ENGINE is None:
        # 从配置构建数据库URL
        db_config = get_db_config()
        db_url = f"mysql+pymysql://{db_config['username']}:{db_config['password']}@{db_config['host']}:{db_config['port']}/{db_config['database']}"

        # 获取连接池配置
        pool_config = get_db_pool_config()
        _DB_ENGINE = create_engine(db_url, **pool_config)
    return _DB_ENGINE

@contextmanager
def get_db_connection():
    """创建数据库连接的上下文管理器"""
    engine = get_db_engine()
    try:
        with engine.connect() as conn:
            yield conn
    except Exception as e:
        log_error(logger, f"数据库连接失败: {str(e)}")
        raise

def format_numeric_columns(df):
    """格式化数值列,统一保留6位小数"""
    if df.empty:
        return df

    numeric_columns = df.select_dtypes(include=['float64', 'float32']).columns
    if len(numeric_columns) > 0:
        df[numeric_columns] = df[numeric_columns].round(6)
    return df

# 缓存导出目录选择结果
_EXPORT_DIR = None

def get_export_directory():
    """获取导出目录（缓存结果）"""
    global _EXPORT_DIR
    if _EXPORT_DIR is None:
        # 从配置获取导出目录路径
        export_config = get_export_config()
        local_dir = export_config['local_dir']
        server_dir = export_config['server_dir']

        # 首先检查服务器目录是否存在
        if os.path.exists(server_dir):
            _EXPORT_DIR = server_dir
            log_progress(logger, f"使用服务器导出路径: {server_dir}")
        # 如果服务器目录不存在，检查本地目录
        elif os.path.exists(local_dir):
            _EXPORT_DIR = local_dir
            log_progress(logger, f"使用本地导出路径: {local_dir}")
        else:
            # 如果两个目录都不存在，创建本地目录并使用它
            os.makedirs(local_dir, exist_ok=True)
            _EXPORT_DIR = local_dir
            log_progress(logger, f"创建并使用本地导出路径: {local_dir}")

    return _EXPORT_DIR

def get_last_trade_day(date_str):
    """
    获取指定日期的上一个交易日
    :param date_str: 日期字符串,格式为'YYYYMMDD'
    :return: 上一个交易日字符串,格式为'YYYYMMDD'
    """
    date = datetime.strptime(date_str, '%Y%m%d')
    
    # 向前查找最多30天,寻找交易日
    for i in range(1, 30):
        prev_date = date - timedelta(days=i)
        prev_date_str = prev_date.strftime('%Y%m%d')
        # 转换为trade_calendar需要的格式 YYYY-MM-DD
        prev_date_formatted = prev_date.strftime('%Y-%m-%d')
        if is_trade_day(prev_date_formatted):
            return prev_date_str
    
    # 如果30天内没找到交易日,返回原日期(异常情况)
    return date_str

def export_latest_macd_data():
    """从macd_analy_di_his表中导出数据到Excel
    :return: bool 导出是否成功
    """
    start_time = time.time()
    log_progress(logger, "=== MACD数据导出开始 ===")

    # 获取导出目录
    base_export_dir = get_export_directory()
    try:
        with get_db_connection() as conn:
            # 首先获取数据库中最新的可用日期
            latest_date_query = text("SELECT MAX(date) FROM macd_analy_di_his")
            latest_date_result = conn.execute(latest_date_query).scalar()

            if latest_date_result:
                query_date_str = latest_date_result
                log_progress(logger, f"使用数据库中最新的可用日期: {query_date_str}")
            else:
                # 如果数据库中没有数据，则使用交易日逻辑
                current_date = datetime.now()
                current_date_str = current_date.strftime('%Y%m%d')
                current_date_formatted = current_date.strftime('%Y-%m-%d')

                # 判断是否是交易日,如果不是则获取上一个交易日
                if not is_trade_day(current_date_formatted):
                    log_progress(logger, f"{current_date_str} 不是交易日,将获取上一个交易日的数据")
                    query_date_str = get_last_trade_day(current_date_str)
                    log_progress(logger, f"获取到的上一个交易日为: {query_date_str}")
                else:
                    query_date_str = current_date_str
                    log_progress(logger, f"今天 {query_date_str} 是交易日")
                
            # 从历史表获取数据
            log_progress(logger, f"开始查询日期 {query_date_str} 的MACD分析数据")
            query_start = time.time()

            query = text("""
                SELECT * FROM macd_analy_di_his
                WHERE date = :date
            """)

            # 执行查询
            df = pd.read_sql(query, conn, params={'date': query_date_str})
            query_time = time.time() - query_start

            log_progress(logger, f"数据查询完成: {len(df)} 条记录，耗时 {query_time:.2f} 秒")

            if not df.empty:
                # 数据处理
                process_start = time.time()
                log_progress(logger, f"开始处理 {len(df)} 条数据")

                # 格式化数值列
                df = format_numeric_columns(df)

                # 重命名列
                df = df.rename(columns=COLUMN_MAPPING)

                # 重排列顺序 - 只选择存在的列
                available_columns = [col for col in COLUMN_ORDER if col in df.columns]
                if len(available_columns) < len(COLUMN_ORDER):
                    missing_columns = set(COLUMN_ORDER) - set(available_columns)
                    log_warning(logger, f"缺少列: {', '.join(missing_columns)}")
                df = df[available_columns]

                process_time = time.time() - process_start
                log_progress(logger, f"数据处理完成，耗时 {process_time:.2f} 秒")

                # 获取数据日期并构建文件名
                latest_date = df['交易日期'].iloc[0]
                # 从日期中提取年月信息并创建子目录
                year_month = latest_date[:6]
                export_dir = os.path.join(base_export_dir, year_month)

                # 确保导出目录存在
                os.makedirs(export_dir, exist_ok=True)

                output_file = os.path.join(export_dir, f'macd_data_{latest_date}.xlsx')

                # 如果文件已存在，删除旧文件
                if os.path.exists(output_file):
                    os.remove(output_file)
                    log_progress(logger, f"删除已存在的旧文件: {output_file}")

                # 导出到Excel
                export_start = time.time()
                log_progress(logger, f"开始导出到Excel文件: {output_file}")

                df.to_excel(output_file, index=False, sheet_name='数据', engine='openpyxl')

                export_time = time.time() - export_start
                total_time = time.time() - start_time

                log_progress(logger, f"Excel导出完成: {len(df)} 条记录")
                log_progress(logger, f"性能统计: 导出耗时 {export_time:.2f}秒, 总耗时 {total_time:.2f}秒")
                log_progress(logger, f"导出速度: {len(df)/export_time:.0f} 条/秒")
                log_progress(logger, f"文件路径: {output_file}")

                return True
            else:
                total_time = time.time() - start_time
                log_warning(logger, f"未找到日期 {query_date_str} 的数据 (总耗时 {total_time:.2f} 秒)")
                return False

    except Exception as e:
        total_time = time.time() - start_time
        log_error(logger, f"导出数据失败 (总耗时 {total_time:.2f} 秒): {str(e)}")
        return False

def main():
    """主函数"""
    log_progress(logger, "=== MACD数据导出任务开始 ===")

    try:
        result = export_latest_macd_data()
        if result:
            log_progress(logger, "=== MACD数据导出任务完成 ===")
            return True
        else:
            log_error(logger, "=== MACD数据导出任务失败 ===")
            send_notification(
                message="导出过程中发生错误,请检查日志获取详细信息",
                title="MACD数据导出失败",
                tags="MACD数据|导出失败"
            )
            return False

    except Exception as e:
        error_msg = str(e)
        log_error(logger, f"=== MACD数据导出任务异常 ===: {error_msg}")
        send_notification(
            message=f"导出过程中发生异常:{error_msg}",
            title="MACD数据导出异常",
            tags="MACD数据|系统异常"
        )
        return False

if __name__ == '__main__':
    import sys

    success = main()
    if not success:
        sys.exit(1)