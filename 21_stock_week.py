import pandas as pd
import tushare as ts
from sqlalchemy import create_engine, text
from sqlalchemy.exc import SQLAlchemyError
import os
import time
from datetime import datetime, timedelta
from dotenv import load_dotenv
from contextlib import contextmanager
from sqlalchemy.pool import QueuePool
import warnings
import pathlib
from urllib3.exceptions import ConnectTimeoutError
from requests.exceptions import RequestException
import configparser
from log_config import setup_logger, log_progress, log_error, log_warning, send_notification
# 导入交易日历模块
from trade_calendar import is_trade_day, get_latest_trade_day, get_next_trade_day, get_friday_of_current_week

# 屏蔽警告信息
warnings.filterwarnings('ignore', category=FutureWarning)
warnings.filterwarnings('ignore', category=UserWarning)

# 初始化环境变量
load_dotenv()

# 获取脚本名称
script_name = os.path.splitext(os.path.basename(__file__))[0]

# 配置日志
logger = setup_logger(script_name)

def load_config():
    """加载配置文件"""
    config = configparser.ConfigParser()
    config_path = os.path.join(os.path.dirname(__file__), 'config.ini')

    if not os.path.exists(config_path):
        log_error(logger, f"配置文件不存在: {config_path}")
        raise FileNotFoundError(f"配置文件不存在: {config_path}")

    try:
        config.read(config_path, encoding='utf-8')
        log_progress(logger, f"成功加载配置文件: {config_path}")
        return config
    except Exception as e:
        log_error(logger, f"加载配置文件失败: {str(e)}")
        raise

def get_config_value(section, key, fallback=None, value_type='str'):
    """
    获取配置值，支持类型转换

    Args:
        section: 配置节
        key: 配置键
        fallback: 默认值
        value_type: 值类型 ('str', 'int', 'float', 'bool')

    Returns:
        配置值
    """
    try:
        if value_type == 'int':
            return config.getint(section, key, fallback=fallback)
        elif value_type == 'float':
            return config.getfloat(section, key, fallback=fallback)
        elif value_type == 'bool':
            return config.getboolean(section, key, fallback=fallback)
        else:
            return config.get(section, key, fallback=fallback)
    except Exception as e:
        log_warning(logger, f"获取配置值失败 {section}.{key}: {str(e)}，使用默认值 {fallback}")
        return fallback

# 加载配置
config = load_config()

def validate_configuration():
    """验证配置完整性"""
    required_configs = [
        ('database', 'host'),
        ('database', 'port'),
        ('database', 'database'),
        ('database', 'user'),
        ('database', 'password'),
        ('tushare', 'token'),
        ('database.pool', 'pool_size'),
        ('database.pool', 'max_overflow'),
        ('database.pool', 'pool_timeout'),
        ('database.pool', 'pool_recycle'),
        ('database.pool', 'pool_pre_ping')
    ]

    missing_configs = []
    for section, key in required_configs:
        try:
            value = config.get(section, key)
            if not value or value.strip() == '':
                missing_configs.append(f"{section}.{key}")
        except Exception:
            missing_configs.append(f"{section}.{key}")

    if missing_configs:
        error_msg = f"缺少必需的配置项: {', '.join(missing_configs)}"
        log_error(logger, error_msg)
        raise ValueError(error_msg)

    log_progress(logger, "周线数据处理配置验证通过")
    return True

# 验证配置
validate_configuration()

# =============================================================================
# 配置管理 - 从config.ini文件读取配置
# =============================================================================
# 这些配置参数从config.ini文件读取，包括数据库连接和API密钥配置
# 避免了硬编码敏感信息，提高了安全性

def get_db_config():
    """从配置文件获取数据库配置"""
    return {
        'user': get_config_value('database', 'user', 'root'),
        'password': get_config_value('database', 'password', '31490600'),
        'host': get_config_value('database', 'host', '**********'),
        'port': get_config_value('database', 'port', '3306'),
        'database': get_config_value('database', 'database', 'instockdb'),
        'charset': 'utf8mb4'
    }

def get_tushare_token():
    """从配置文件获取Tushare Token"""
    return get_config_value('tushare', 'token', '5aba669a485730beded0d604bd8ccfdaca39f8c9c09ef2d0ed78ed77')

# API访问控制
ENABLE_RATE_LIMIT = True  # 是否启用API访问频率限制
API_RATE_LIMIT = 800  # 每分钟最大请求次数
API_RATE_INTERVAL = 60  # 时间窗口(秒)

class RateLimiter:
    def __init__(self, limit, interval):
        self.limit = limit
        self.interval = interval
        self.requests = []
        self.last_reset = time.time()
        self.cooldown_time = 0
        
    def wait_if_needed(self):
        now = time.time()
        
        # 如果在冷却期，等待冷却结束
        if self.cooldown_time > 0:
            sleep_time = self.cooldown_time - (now - self.last_reset)
            if sleep_time > 0:
                time.sleep(sleep_time)
            self.cooldown_time = 0
            self.requests = []
            self.last_reset = time.time()
            return
        
        # 清理过期的请求记录
        self.requests = [req_time for req_time in self.requests 
                        if now - req_time < self.interval]
        
        if len(self.requests) >= self.limit:
            # 等待最早的请求过期
            sleep_time = self.interval - (now - self.requests[0])
            if sleep_time > 0:
                time.sleep(sleep_time)
            self.requests = self.requests[1:]
        
        self.requests.append(now)
    
    def trigger_cooldown(self, duration=60):
        """触发冷却期"""
        self.cooldown_time = duration
        self.last_reset = time.time()
        self.requests = []
        log_progress(logger, f"触发API冷却期 {duration} 秒")

# 创建限速器实例
rate_limiter = RateLimiter(API_RATE_LIMIT, API_RATE_INTERVAL)

# 数据库连接池配置
DB_POOL_SIZE = 10
DB_MAX_OVERFLOW = 20
DB_POOL_TIMEOUT = 30

# 表结构定义
TABLE_SCHEMA = """
CREATE TABLE IF NOT EXISTS `ts_stock_weekly` (
    `id` BIGINT AUTO_INCREMENT PRIMARY KEY,
    `ts_code` VARCHAR(10) NOT NULL COMMENT 'TS代码',
    `stock_code` VARCHAR(6) NOT NULL COMMENT '股票代码',
    `name` VARCHAR(50) NOT NULL COMMENT '股票名称',
    `trade_date` DATE NOT NULL COMMENT '交易日期',
    `open` DECIMAL(10,4) COMMENT '开盘价(前复权)',
    `high` DECIMAL(10,4) COMMENT '最高价(前复权)',
    `low` DECIMAL(10,4) COMMENT '最低价(前复权)',
    `close` DECIMAL(10,4) COMMENT '收盘价(前复权)',
    `vol` DECIMAL(20,2) COMMENT '成交量',
    `created_at` TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    `updated_at` TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    UNIQUE KEY `uk_ts_code_trade_date` (`ts_code`, `trade_date`),
    KEY `idx_stock_code` (`stock_code`),
    KEY `idx_trade_date` (`trade_date`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='股票周线行情表(前复权)';
"""

@contextmanager
def db_connection():
    """数据库连接上下文管理"""
    # 从配置文件获取数据库配置
    db_config = get_db_config()

    # 从配置文件获取连接池参数
    pool_size = get_config_value('database.pool', 'pool_size', DB_POOL_SIZE, 'int')
    max_overflow = get_config_value('database.pool', 'max_overflow', DB_MAX_OVERFLOW, 'int')
    pool_timeout = get_config_value('database.pool', 'pool_timeout', DB_POOL_TIMEOUT, 'int')
    pool_recycle = get_config_value('database.pool', 'pool_recycle', 1800, 'int')
    pool_pre_ping = get_config_value('database.pool', 'pool_pre_ping', True, 'bool')

    engine = create_engine(
        f"mysql+pymysql://{db_config['user']}:{db_config['password']}@"
        f"{db_config['host']}:{db_config['port']}/{db_config['database']}?charset={db_config['charset']}",
        poolclass=QueuePool,
        pool_size=pool_size,
        max_overflow=max_overflow,
        pool_timeout=pool_timeout,
        pool_recycle=pool_recycle,
        pool_pre_ping=pool_pre_ping  # 在使用连接前先测试连接是否有效
    )
    conn = None
    try:
        conn = engine.connect()
        yield conn
    except SQLAlchemyError as e:
        log_error(logger, f"数据库连接失败: {str(e)}")
        raise
    finally:
        if conn:
            conn.close()
        engine.dispose()

def init_database():
    """初始化数据库表结构"""
    try:
        with db_connection() as conn:
            conn.execute(text(TABLE_SCHEMA))
            log_progress(logger, "数据库表初始化完成")
    except Exception as e:
        log_error(logger, f"数据库初始化失败: {str(e)}")
        raise

def retry_api_call(func, *args, max_retries=5, initial_delay=30, **kwargs):
    """通用API调用重试函数"""
    retry_count = 0
    while True:  # 无限重试，直到成功
        try:
            if ENABLE_RATE_LIMIT:
                rate_limiter.wait_if_needed()
            return func(*args, **kwargs)
        except (ConnectTimeoutError, RequestException) as e:
            retry_count += 1
            wait_time = initial_delay * (2 ** (retry_count - 1))  # 指数退避策略
            if retry_count >= max_retries:
                wait_time = 300  # 达到最大重试次数后，固定等待5分钟
                retry_count = 0  # 重置重试计数
            log_progress(logger, f"连接超时, 第{retry_count}次重试, 等待{wait_time}秒: {str(e)}")
            time.sleep(wait_time)
        except Exception as e:
            error_msg = str(e)
            if "每分钟最多访问该接口" in error_msg:
                rate_limiter.trigger_cooldown(45)  # 触发90秒冷却期
                continue
            retry_count += 1
            wait_time = initial_delay * (2 ** (retry_count - 1))
            if retry_count >= max_retries:
                wait_time = 300
                retry_count = 0
            log_error(logger, f"API调用失败: {error_msg}, 等待{wait_time}秒后重试")
            time.sleep(wait_time)

def get_stocks():
    """获取所有上市公司列表"""
    def _get_stocks():
        df = pro.stock_basic(exchange='', list_status='L', 
                          fields='ts_code,symbol,name')
        if df is None or df.empty:
            raise Exception("获取股票列表返回空数据")
        return df
    
    return retry_api_call(_get_stocks)

def validate_weekly_data(df, last_trading_day=None):
    """数据校验与清洗"""
    if df.empty:
        return df
    
    # 创建数据框的副本
    df = df.copy()
    
    # 转换日期格式
    df['trade_date'] = pd.to_datetime(df['trade_date'], format='%Y%m%d').dt.date
    
    # 如果提供了本周最后交易日，则将本周数据的交易日期替换为实际的最后交易日
    if last_trading_day:
        # 将last_trading_day转换为date类型
        last_date = datetime.strptime(last_trading_day, '%Y%m%d').date()
        # 获取当前周的周五日期
        current_friday = get_friday_of_current_week(datetime.now()).date()
        
        # 只替换本周数据的日期（通过比较trade_date是否等于本周五来识别）
        df.loc[df['trade_date'] == current_friday, 'trade_date'] = last_date
        log_progress(logger, f"已将本周{len(df[df['trade_date'] == last_date])}条数据的交易日期更新为实际的最后交易日: {last_date}")
    
    # 批量重命名前复权字段
    rename_mapping = {
        'close_qfq': 'close',
        'open_qfq': 'open',
        'high_qfq': 'high',
        'low_qfq': 'low'
    }
    # 只重命名存在的列
    existing_renames = {k: v for k, v in rename_mapping.items() if k in df.columns}
    if existing_renames:
        df = df.rename(columns=existing_renames)
    
    # 数值类型转换
    numeric_cols = ['close', 'open', 'high', 'low', 'vol']
    for col in numeric_cols:
        if col in df.columns:
            df[col] = pd.to_numeric(df[col], errors='coerce')
    
    # 删除重复项
    df = df.drop_duplicates(subset=['ts_code', 'trade_date'], keep='first')
    
    return df.dropna(subset=['ts_code', 'trade_date'])

def get_trade_calendar(pro, start_date, end_date):
    """获取交易日历，筛选出每周最后一个交易日（周线日期）"""
    def _get_calendar():
        df = pro.trade_cal(exchange='', start_date=start_date, end_date=end_date)
        if df is None or df.empty:
            raise Exception("获取交易日历返回空数据")
        return df
    
    calendar_df = retry_api_call(_get_calendar)
    
    # 筛选出开市日
    trading_days = calendar_df[calendar_df['is_open'] == 1]['cal_date'].tolist()
    
    # 转换为日期对象以便处理
    trading_days = [datetime.strptime(day, '%Y%m%d') for day in trading_days]
    trading_days.sort()
    
    # 识别每周最后一个交易日
    weekly_last_days = []
    for i in range(len(trading_days)):
        # 如果是列表中的最后一个日期，或者下一个日期与当前日期不在同一周，则当前日期是本周最后一个交易日
        if i == len(trading_days) - 1 or trading_days[i].isocalendar()[1] != trading_days[i+1].isocalendar()[1]:
            weekly_last_days.append(trading_days[i].strftime('%Y%m%d'))
    
    return weekly_last_days

def get_available_dates(pro, ts_code, start_date, end_date):
    """获取指定股票代码在日期范围内的所有可用交易日期"""
    def _get_dates():
        # 获取周线数据以识别有效的交易日期
        # 使用stk_week_month_adj接口获取周线数据
        df = pro.stk_week_month_adj(ts_code=ts_code,
                                   start_date=start_date,
                                   end_date=end_date,
                                   freq='week')
        if df is None or df.empty:
            raise Exception("获取周线数据返回空数据")
        return df['trade_date'].unique().tolist()

    return retry_api_call(_get_dates)

def get_weekly_data(pro, trade_dates):
    """按交易日期获取所有股票的周线数据"""
    all_weekly_data = []
    total_dates = len(trade_dates)
    
    for i, trade_date in enumerate(trade_dates):
        try:
            def _get_weekly_by_date():
                df = pro.stk_week_month_adj(**{
                    "ts_code": "",
                    "trade_date": trade_date,
                    "start_date": "",
                    "end_date": "",
                    "freq": "week",
                }, fields=[
                    "ts_code", "trade_date", "close_qfq", "open_qfq", "high_qfq", "low_qfq", "vol"
                ])

                if df is None:
                    raise Exception(f"获取日期{trade_date}周线数据返回空")
                return df
            
            df = retry_api_call(_get_weekly_by_date)
            
            if not df.empty:
                all_weekly_data.append(df)
                log_progress(logger, f"已获取 {i+1}/{total_dates} 个交易周的数据，当前日期: {trade_date}，记录数: {len(df)}")
            else:
                log_progress(logger, f"日期 {trade_date} 没有周线数据")
            
        except Exception as e:
            log_error(logger, f"获取日期{trade_date}周线数据失败: {str(e)}")
    
    # 合并所有交易日的周线数据
    if all_weekly_data:
        return pd.concat(all_weekly_data, ignore_index=True)
    return pd.DataFrame()

# 初始化tushare接口
tushare_token = get_tushare_token()
ts.set_token(tushare_token)
pro = ts.pro_api(tushare_token)

def truncate_table():
    """清空数据表"""
    try:
        with db_connection() as conn:
            conn.execute(text("TRUNCATE TABLE ts_stock_weekly"))
            log_progress(logger, "数据表已清空")
    except Exception as e:
        log_error(logger, f"清空数据表失败: {str(e)}")
        raise

def save_weekly_data(df):
    """保存数据到数据库"""
    if df.empty:
        return
    
    max_retries = 3
    # 每批写入的记录数减小到100条
    batch_size = 5000
    total_records = len(df)
    processed_records = 0
    
    log_progress(logger, f"开始保存数据到数据库，总记录数: {total_records}，批次大小: {batch_size}")
    
    # 按批次保存数据
    for i in range(0, total_records, batch_size):
        batch_df = df.iloc[i:i+batch_size]
        retry_count = 0
        
        while retry_count < max_retries:
            try:
                with db_connection() as conn:
                    batch_df.to_sql(
                        'ts_stock_weekly',
                        conn,
                        if_exists='append',
                        index=False,
                        chunksize=batch_size
                    )
                    
                    processed_records += len(batch_df)
                    if processed_records % (batch_size * 10) == 0 or processed_records == total_records:
                        log_progress(logger, f"已保存 {processed_records}/{total_records} 条记录")
                    
                    break  # 保存成功，跳出重试循环
                    
            except SQLAlchemyError as e:
                retry_count += 1
                if retry_count < max_retries:
                    log_progress(logger, f"第 {i//batch_size + 1} 批数据写入失败，正在进行第 {retry_count} 次重试: {str(e)}")
                    time.sleep(1)
                else:
                    log_error(logger, f"第 {i//batch_size + 1} 批数据写入失败，已重试 {max_retries} 次: {str(e)}")
                    raise
    
    log_progress(logger, f"数据保存完成，共写入 {processed_records} 条记录")

def get_and_save_weekly_data():
    """获取和保存股票周线数据"""
    try:
        # 清空数据表
        truncate_table()
        
        # 获取所有上市公司列表
        stocks = get_stocks()
        
        # 计算日期范围(近4年)
        today = datetime.now()
        end_date = today.strftime('%Y%m%d')  # 先使用当前日期获取已有数据
        start_date = (today - timedelta(days=2500)).strftime('%Y%m%d')

        # 获取所有交易日历
        log_progress(logger, "获取交易日历...")
        trading_calendar = retry_api_call(lambda: pro.trade_cal(exchange='', start_date=start_date, end_date=end_date))
        # 筛选出开市日
        trading_days = trading_calendar[trading_calendar['is_open'] == 1]['cal_date'].tolist()
        trading_days.sort()

        # 使用000001.SZ股票获取所有可用的交易日期
        log_progress(logger, "开始获取可用交易日期...")
        sample_stock = "000001.SZ"  # 使用平安银行作为样本股票
        available_dates = get_available_dates(pro, sample_stock, start_date, end_date)
        available_dates.sort()  # 按时间顺序排列

        # 检查当前周的最后交易日
        current_weekday = today.weekday()  # 0-6 表示周一到周日
        log_progress(logger, f"当前是周{current_weekday + 1}")

        # 使用专业的交易日历模块检查本周最后一个交易日
        log_progress(logger, "使用交易日历模块检查本周交易日...")

        # 1. 获取本周周五的日期作为API查询日期
        friday_of_week = get_friday_of_current_week(today)
        friday_date_str = friday_of_week.strftime('%Y%m%d')
        log_progress(logger, f"本周周五是: {friday_of_week.strftime('%Y-%m-%d')} (用于API查询)")

        # 2. 获取本周实际的最后交易日（用于数据库存储）
        last_trading_day = None
        check_date = friday_of_week
        monday_of_week = friday_of_week - timedelta(days=4)  # 本周一

        # 从周五向前查找，但不超出本周范围
        while check_date >= monday_of_week:
            if is_trade_day(check_date):
                last_trading_day = check_date.strftime('%Y%m%d')
                log_progress(logger, f"找到本周最后交易日: {check_date.strftime('%Y-%m-%d')} (周{check_date.weekday() + 1})")
                break
            check_date -= timedelta(days=1)

        if not last_trading_day:
            log_progress(logger, "本周没有找到交易日")

        # 统一使用周五日期查询API
        query_date = friday_date_str

        # 确保周五日期在查询列表中
        if query_date not in available_dates:
            available_dates.append(query_date)
            available_dates.sort()
            log_progress(logger, f"已将本周周五日期 {query_date} 添加到查询列表")

        log_progress(logger, f"获取到 {len(available_dates)} 个可用交易日期")

        # 按交易日期获取所有股票的周线数据
        log_progress(logger, "开始获取周线数据...")

        df_weekly = get_weekly_data(pro, available_dates)

        if df_weekly.empty:
            raise Exception("未获取到周线数据")
            
        log_progress(logger, f"周线数据获取完成，共 {len(df_weekly)} 条记录")
        
        # 添加股票代码和名称
        log_progress(logger, "开始添加股票信息...")
        
        # 合并股票基本信息
        log_progress(logger, f"周线数据中共有 {len(df_weekly['ts_code'].unique())} 个唯一股票代码")
        
        # 使用merge而不是join，明确指定如何合并
        df_weekly = pd.merge(
            df_weekly, 
            stocks[['ts_code', 'symbol', 'name']], 
            on='ts_code', 
            how='inner'  # 使用inner join，只保留当前存在的股票
        )
        df_weekly = df_weekly.rename(columns={'symbol': 'stock_code'})
        
        # 数据清洗和保存
        log_progress(logger, "开始数据清洗...")
        clean_df = validate_weekly_data(df_weekly, last_trading_day)
        
        # 只保留需要的列
        columns_to_save = ['ts_code', 'stock_code', 'name', 'trade_date', 'open', 'high', 'low', 'close', 'vol']
        clean_df = clean_df[columns_to_save]
        
        # 检查最终是否还有空值
        null_check = clean_df[clean_df.isnull().any(axis=1)]
        if not null_check.empty:
            log_progress(logger, f"完成处理后仍有 {len(null_check)} 条含空值的记录，将被过滤")
            clean_df = clean_df.dropna()
        
        # 保存数据
        log_progress(logger, "开始保存数据...")
        save_weekly_data(clean_df)
        
        log_progress(logger, f"所有数据处理完成，共处理 {len(clean_df)} 条记录")
        
    except Exception as e:
        log_error(logger, f"数据获取过程发生错误: {str(e)}")
        raise

if __name__ == '__main__':
    try:
        # 移除开始时的通知
        # send_notification(
        #     message="开始获取股票周线数据...",
        #     title="周线数据同步开始",
        #     tags="周线数据|开始"
        # )
        
        init_database()
        get_and_save_weekly_data()
        
        # 移除成功时的通知
        # send_notification(
        #     message="股票周线数据同步已完成",
        #     title="周线数据同步成功",
        #     tags="周线数据|完成"
        # )
    except Exception as e:
        error_msg = str(e)
        log_error(logger, f"程序执行异常: {error_msg}")
        send_notification(
            message=f"周线数据同步失败: {error_msg}",
            title="周线数据同步异常",
            tags="周线数据|异常"
        )
        exit(1) 