import pandas as pd
from sqlalchemy import create_engine
from datetime import datetime
import numpy as np
from scipy.signal import savgol_filter
from MyTT import EMA  # 引入 MyTT 库中 EMA 指标计算函数
from pyecharts import options as opts
from pyecharts.charts import Grid, Line, Bar, Kline
from pyecharts.commons.utils import JsCode
from IPython.display import HTML, display

def display_chart_in_jupyter(chart):
    """在Jupyter中直接显示pyecharts图表"""
    html_content = chart.render_embed()
    display(HTML(html_content))
# 重用原有的数据库读取函数
def read_stock_data_from_db(db_url, stock_code, start_date=None, end_date=None):
    """
    从数据库中读取指定股票代码和日期区间的数据
    :param db_url: 数据库连接URL,例如 'mysql+pymysql://user:password@host:port/database'
    :param stock_code: 股票代码,如'000001'
    :param start_date: 开始日期,格式为'YYYY-MM-DD',可选
    :param end_date: 结束日期,格式为'YYYY-MM-DD',可选
    :return: 返回查询结果,如果没有找到返回None
    """
    try:
        # 创建数据库引擎
        engine = create_engine(db_url)
        
        # 构建查询语句
        query = f"""
            SELECT stock_code,name,trade_date,close
            FROM ts_stock_daily 
            WHERE stock_code = '{stock_code}'
        """
        
        # 添加日期区间条件
        if start_date and end_date:
            query += f" AND trade_date BETWEEN '{start_date}' AND '{end_date}'"
        elif start_date:
            query += f" AND trade_date >= '{start_date}'"
        elif end_date:
            query += f" AND trade_date <= '{end_date}'"
        
        # 按日期排序
        query += " ORDER BY trade_date DESC"
        
        # 执行查询
        df = pd.read_sql(query, engine)
        
        # 返回查询结果
        if not df.empty:
            return df
        else:
            print(f"未找到股票 {stock_code} 在指定日期区间的数据")
            return None
    except Exception as e:
        print(f"从数据库读取数据失败: {str(e)}")
        return None

def get_stock_data(stock_code, start_date=None, end_date=None):
    """
    获取股票数据的封装函数(DB_URL 内置)
    :param stock_code: 股票代码
    :param start_date: 开始日期(可选)
    :param end_date: 结束日期(可选)
    :return: 返回股票数据DataFrame
    """
    # 内置数据库连接URL
    DB_URL = "mysql+pymysql://root:31490600@10.10.10.2:3306/instockdb"
    
    # 调用 read_stock_data_from_db 函数
    result = read_stock_data_from_db(DB_URL, stock_code, start_date, end_date)
    if result is not None:
        return result
    else:
        return pd.DataFrame()  # 返回空DataFrame

def plot_stock_chart_echarts(stock_code="601398", display_points=250, longterm_period=26, shortterm_period=13, 
                            window_size=66, smooth_order0=0, smooth_order1=1, start_date=None, end_date=None):
    """
    使用pyecharts绘制股票分析图
    :param stock_code: 股票代码
    :param display_points: 显示的数据点数量
    :param longterm_period: 长期EMA周期
    :param shortterm_period: 短期EMA周期
    :param window_size: 平滑处理窗口大小
    :param smooth_order0: 0阶平滑系数
    :param smooth_order1: 1阶平滑系数
    :param start_date: 开始日期，格式为'YYYY-MM-DD'
    :param end_date: 结束日期，格式为'YYYY-MM-DD'
    :return: pyecharts的Grid对象
    """
    # 调用 get_stock_data 函数从数据库读取数据，传入日期参数
    df = get_stock_data(stock_code, start_date, end_date)

    # 检查数据是否为空
    if df.empty:
        raise ValueError("数据为空,请检查股票代码或数据源.")

    # 数据预处理
    df = df.sort_index(ascending=False)  # 按日期降序排列
    # 提取收盘价数据
    close_prices = df['close'].values  # 直接提取收盘价数据
    stock_name = df['name'].iloc[0] if 'name' in df.columns else ""

    # 使用指数移动平均(EMA)替换原来的均值计算方式
    ema_long = EMA(close_prices, longterm_period)
    ema_short = EMA(close_prices, shortterm_period)
    # 对齐:跳过EMA计算初期的偏移(longterm-1个数据)
    align_offset = longterm_period - 1
    ema26_aligned = ema_long[align_offset:]
    ema13_aligned = ema_short[align_offset:]

    # 差值数组
    ema_diff = ema13_aligned - ema26_aligned

    # 设置图像显示的数据点数量
    data_length = len(close_prices)
    start_idx = max(0, data_length - display_points)

    # 获取日期、价格等数据
    dates = df['trade_date'].iloc[start_idx:].values
    prices = close_prices[start_idx:]
    
    # 获取对齐后的数据点索引
    aligned_indices = np.arange(align_offset, len(close_prices))
    display_mask = aligned_indices >= start_idx

    # 准备显示的数据
    display_dates = dates
    display_prices = prices
    display_ema26 = ema26_aligned[display_mask]
    display_ema13 = ema13_aligned[display_mask]
    display_ema_diff = ema_diff[display_mask]
    
    # 计算差分率
    ema_diff_rate = np.diff(ema_diff)
    
    # 对齐差分率数据
    aligned_indices_diff = aligned_indices[:-1]
    display_mask_diff = aligned_indices_diff >= start_idx
    display_diff_rate = ema_diff_rate[display_mask_diff]
    
    # 平滑处理
    # 确保window_size为奇数
    if window_size % 2 == 0:
        window_size += 1
    
    # 确保window_size不大于数据长度
    if window_size > len(ema_diff):
        window_size = len(ema_diff) if len(ema_diff) % 2 == 1 else len(ema_diff) - 1
        if window_size < 3:
            window_size = 3
    
    smoothed_diff = savgol_filter(ema_diff, window_size, smooth_order0)
    smooth_diff_order1 = savgol_filter(ema_diff, window_size, smooth_order1)
    
    display_smoothed_diff = smoothed_diff[display_mask]
    display_smooth_diff_order1 = smooth_diff_order1[display_mask]
    
    # 计算正负差分率
    positive_diff_rate = np.where(display_diff_rate > 0, display_diff_rate, 0)
    negative_diff_rate = np.where(display_diff_rate < 0, display_diff_rate, 0)

    # 根据0阶信号线的位置，重新分配正负区域
    signal_below_zero = display_smoothed_diff < 0
    signal_above_zero = display_smoothed_diff > 0
    signal_below_zero_truncated = signal_below_zero[:-1]  # 截断以匹配差分率长度
    signal_above_zero_truncated = signal_above_zero[:-1]  # 截断以匹配差分率长度

    # 当0阶信号线在0以下时，将正值作为负值处理（用黑色显示）
    for i in range(len(signal_below_zero_truncated)):
        if signal_below_zero_truncated[i]:
            negative_diff_rate[i] += positive_diff_rate[i]  # 将正值添加到负值
            positive_diff_rate[i] = 0  # 清除正值

    # 当0阶信号线在0以上时，将负值作为正值处理（用红色显示）
    for i in range(len(signal_above_zero_truncated)):
        if signal_above_zero_truncated[i]:
            positive_diff_rate[i] += negative_diff_rate[i]  # 将负值添加到正值
            negative_diff_rate[i] = 0  # 清除负值

    # 创建价格线图表
    price_chart = (
        Line()
        .add_xaxis(xaxis_data=list(display_dates))
        .add_yaxis(
            series_name="价格",
            y_axis=list(display_prices),
            symbol="emptyCircle",
            symbol_size=3,
            is_symbol_show=False,
            linestyle_opts=opts.LineStyleOpts(width=1.5),
            label_opts=opts.LabelOpts(is_show=False),
            itemstyle_opts=opts.ItemStyleOpts(color="#1f77b4"),
            areastyle_opts=opts.AreaStyleOpts(opacity=0.1, color="#1f77b4"),  # 添加区域填充
        )
        .add_yaxis(
            series_name=f"EMA{longterm_period}",
            y_axis=list(display_ema26),
            symbol="emptyCircle",
            symbol_size=3,
            is_symbol_show=False,
            linestyle_opts=opts.LineStyleOpts(width=2),
            label_opts=opts.LabelOpts(is_show=False),
            itemstyle_opts=opts.ItemStyleOpts(color="#ff7f0e"),
        )
        .add_yaxis(
            series_name=f"EMA{shortterm_period}",
            y_axis=list(display_ema13),
            symbol="emptyCircle",
            symbol_size=3,
            is_symbol_show=False,
            linestyle_opts=opts.LineStyleOpts(width=2),
            label_opts=opts.LabelOpts(is_show=False),
            itemstyle_opts=opts.ItemStyleOpts(color="#2ca02c"),
        )
        .set_global_opts(
            title_opts=opts.TitleOpts(
                title="",
                subtitle="",
            ),
            tooltip_opts=opts.TooltipOpts(
                trigger="axis",
                axis_pointer_type="cross",
                background_color="rgba(255, 255, 255, 0.9)",
                border_width=1,
                border_color="#ccc",
                textstyle_opts=opts.TextStyleOpts(color="#000"),
                formatter=JsCode(
                    """
                    function(params) {
                        let result = params[0].name + '<br/>';
                        params.forEach(param => {
                            let color = param.color;
                            let seriesName = param.seriesName;
                            let value = param.value;
                            result += '<span style="display:inline-block;width:10px;height:10px;border-radius:50%;background-color:' + color + ';margin-right:5px;"></span>';
                            result += seriesName + ': ' + value + '<br/>';
                        });
                        return result;
                    }
                    """
                ),
            ),
            legend_opts=opts.LegendOpts(
                pos_top="12%",  # 位于第一个图表的左上角
                pos_left="20%",  # 增加左侧距离，避免与Y轴标签重叠
                textstyle_opts=opts.TextStyleOpts(font_size=12),
            ),
            xaxis_opts=opts.AxisOpts(
                type_="category",
                boundary_gap=False,
                axislabel_opts=opts.LabelOpts(is_show=False),  # 不在第一个图表显示x轴标签
                axisline_opts=opts.AxisLineOpts(is_show=True),
                axistick_opts=opts.AxisTickOpts(is_show=True),
                splitline_opts=opts.SplitLineOpts(is_show=False),
            ),
            yaxis_opts=opts.AxisOpts(
                type_="value",
                position="left",  # 从右侧移到左侧
                offset=0,
                axisline_opts=opts.AxisLineOpts(is_show=True),
                axistick_opts=opts.AxisTickOpts(is_show=True),
                axislabel_opts=opts.LabelOpts(
                    margin=15,
                    formatter=JsCode("""
                    function(value) {
                        if(value == 0) return '0.00';
                        return (value > 0 ? '+' : '') + value.toFixed(2);
                    }
                    """)
                ),
                # 设置y轴范围，不从0开始，而是从接近最小值的位置开始
                min_=min(display_prices) * 0.95,  # 最小价格的95%作为下限
                max_=max(display_prices) * 1.05,  # 最大价格的105%作为上限
                splitline_opts=opts.SplitLineOpts(is_show=False),
            ),
        )
    )

    # 创建差分率柱状图
    scaling_factor = 10  # 与原来保持一致的放大因子
    
    # 计算差分率的最大最小值（包括正值和负值）
    diff_rate_max = max(max(positive_diff_rate), abs(min(negative_diff_rate))) * scaling_factor
    diff_rate_min = -diff_rate_max  # 对称的范围
    
    diff_rate_chart = (
        Line()
        .add_xaxis(xaxis_data=list(display_dates)[1:])  # 差分率比原始数据少一个点
        .add_yaxis(
            series_name="上升趋势",
            y_axis=[float(v * scaling_factor) for v in positive_diff_rate],
            symbol="emptyCircle",
            symbol_size=3,
            is_symbol_show=False,
            label_opts=opts.LabelOpts(is_show=False),
            itemstyle_opts=opts.ItemStyleOpts(color="#d62728"),  # 红色
            linestyle_opts=opts.LineStyleOpts(width=3.0),
        )
        .add_yaxis(
            series_name="下降趋势",
            y_axis=[float(v * scaling_factor) for v in negative_diff_rate],
            symbol="emptyCircle",
            symbol_size=3,
            is_symbol_show=False,
            label_opts=opts.LabelOpts(is_show=False),
            itemstyle_opts=opts.ItemStyleOpts(color="#000000"),  # 将绿色(#26A69A)改为黑色
            linestyle_opts=opts.LineStyleOpts(width=3.0),
        )
        .set_global_opts(
            xaxis_opts=opts.AxisOpts(
                type_="category",
                boundary_gap=True,
                axislabel_opts=opts.LabelOpts(is_show=False),  # 不在第二个图表显示x轴标签
                axisline_opts=opts.AxisLineOpts(is_show=True),
                axistick_opts=opts.AxisTickOpts(is_show=True),
                splitline_opts=opts.SplitLineOpts(is_show=False),
            ),
            yaxis_opts=opts.AxisOpts(
                name="差分率",
                position="left",  # 从右侧移到左侧
                offset=0,
                axisline_opts=opts.AxisLineOpts(is_show=True),
                axistick_opts=opts.AxisTickOpts(is_show=True),
                axislabel_opts=opts.LabelOpts(
                    margin=15,
                    formatter=JsCode("""
                    function(value) {
                        if(value == 0) return '0.00';
                        return (value > 0 ? '+' : '') + value.toFixed(2);
                    }
                    """)
                ),
                # 设置合适的范围，使图表更易于观察
                min_=diff_rate_min * 1.1,
                max_=diff_rate_max * 1.1,
                splitline_opts=opts.SplitLineOpts(is_show=False),
            ),
            legend_opts=opts.LegendOpts(
                pos_top="42%",  # 位于第二个图表的左上角
                pos_left="20%",  # 增加左侧距离，避免与Y轴标签重叠
                textstyle_opts=opts.TextStyleOpts(font_size=12),
            ),
            tooltip_opts=opts.TooltipOpts(
                trigger="axis",
                axis_pointer_type="shadow",
                background_color="rgba(255, 255, 255, 0.9)",
                border_width=1,
                border_color="#ccc",
                textstyle_opts=opts.TextStyleOpts(color="#000"),
                formatter=JsCode(
                    """
                    function(params) {
                        let result = params[0].name + '<br/>';
                        let upValue = 0;
                        let downValue = 0;
                        
                        params.forEach(param => {
                            let color = param.color;
                            let seriesName = param.seriesName;
                            let value = param.value;
                            
                            if (seriesName === '上升趋势') {
                                upValue = value;
                            } else if (seriesName === '下降趋势') {
                                downValue = value;
                            }
                            
                            result += '<span style="display:inline-block;width:10px;height:10px;border-radius:50%;background-color:' + color + ';margin-right:5px;"></span>';
                            result += seriesName + ': ' + value.toFixed(4) + '<br/>';
                        });
                        
                        // 计算总差分率
                        let totalDiff = upValue + downValue;
                        result += '<hr style="margin: 5px 0" />';
                        result += '总差分率: ' + totalDiff.toFixed(4);
                        
                        return result;
                    }
                    """
                ),
            ),
        )
    )

    # 创建MACD图表
    # 计算MACD数据的最大最小值（包括所有三条线）
    macd_values = list(display_ema_diff) + list(display_smooth_diff_order1) + list(display_smoothed_diff)
    macd_max = max(macd_values) * 2 * 1.3  # 与放大因子一致
    macd_min = min(macd_values) * 2 * 1.3
    
    macd_chart = (
        Line()
        .add_xaxis(xaxis_data=list(display_dates))
        .add_yaxis(
            series_name="MACD (EMA13-EMA26)",
            y_axis=list(map(lambda x: float(x) * 2, display_ema_diff)),  # 添加乘以2的放大因子
            symbol="emptyCircle",
            symbol_size=3,
            is_symbol_show=False,
            linestyle_opts=opts.LineStyleOpts(width=2.0),
            label_opts=opts.LabelOpts(is_show=False),
            itemstyle_opts=opts.ItemStyleOpts(color="#9467bd"),
        )
        .add_yaxis(
            series_name=f"Signal Line (Order: {smooth_order1})",
            y_axis=list(map(lambda x: float(x) * 2, display_smooth_diff_order1)),  # 添加乘以2的放大因子
            symbol="emptyCircle",
            symbol_size=3,
            is_symbol_show=False,
            linestyle_opts=opts.LineStyleOpts(width=3.0),
            label_opts=opts.LabelOpts(is_show=False),
            itemstyle_opts=opts.ItemStyleOpts(color="#DC143C"),
        )
        .add_yaxis(
            series_name=f"Smooth Line (Order: {smooth_order0})",
            y_axis=list(map(lambda x: float(x) * 2, display_smoothed_diff)),  # 添加乘以2的放大因子
            symbol="emptyCircle",
            symbol_size=3,
            is_symbol_show=False,
            linestyle_opts=opts.LineStyleOpts(width=3.5),
            label_opts=opts.LabelOpts(is_show=False),
            itemstyle_opts=opts.ItemStyleOpts(color="#1E90FF"),
        )
        .set_global_opts(
            xaxis_opts=opts.AxisOpts(
                type_="category",
                boundary_gap=False,
                axislabel_opts=opts.LabelOpts(
                    is_show=True,
                    interval=max(len(display_dates) // 10, 1),  # 每10个点显示一个标签
                    formatter=JsCode("function(value){return value.replace(/-/g,'');}"),  # 移除横线显示为yyyyMMdd格式
                    rotate=45,
                    font_size=10,
                    margin=8
                ),
                axisline_opts=opts.AxisLineOpts(is_show=True),
                axistick_opts=opts.AxisTickOpts(is_show=True),
                splitline_opts=opts.SplitLineOpts(is_show=False),
            ),
            yaxis_opts=opts.AxisOpts(
                name="MACD",
                position="left",  # 从右侧移到左侧
                offset=0,
                axisline_opts=opts.AxisLineOpts(is_show=True),
                axistick_opts=opts.AxisTickOpts(is_show=True),
                axislabel_opts=opts.LabelOpts(
                    margin=15,
                    formatter=JsCode("""
                    function(value) {
                        if(value == 0) return '0.00';
                        return (value > 0 ? '+' : '') + value.toFixed(2);
                    }
                    """)
                ),
                # 设置合适的范围，确保零线在中间位置
                min_=min(macd_min, -abs(macd_max) * 0.2),  # 确保零线不在最底部
                max_=max(macd_max, abs(macd_min) * 0.2),   # 确保零线不在最顶部
                splitline_opts=opts.SplitLineOpts(is_show=False),
            ),
            legend_opts=opts.LegendOpts(
                pos_top="70%",  # 位于第三个图表的左上角
                pos_left="20%",  # 增加左侧距离，避免与Y轴标签重叠
                textstyle_opts=opts.TextStyleOpts(font_size=12),
            ),
            tooltip_opts=opts.TooltipOpts(
                trigger="axis",
                axis_pointer_type="cross",
                background_color="rgba(255, 255, 255, 0.9)",
                border_width=1,
                border_color="#ccc",
                textstyle_opts=opts.TextStyleOpts(color="#000"),
                formatter=JsCode(
                    """
                    function(params) {
                        let result = params[0].name + '<br/>';
                        
                        // 记录各项MACD数据
                        let macdValue = null;
                        let signalValue = null;
                        let smoothValue = null;
                        
                        params.forEach(param => {
                            let color = param.color;
                            let seriesName = param.seriesName;
                            let value = param.value;
                            
                            if (seriesName.startsWith('MACD')) {
                                macdValue = value;
                            } else if (seriesName.startsWith('Signal')) {
                                signalValue = value;
                            } else if (seriesName.startsWith('Smooth')) {
                                smoothValue = value;
                            }
                            
                            result += '<span style="display:inline-block;width:10px;height:10px;border-radius:50%;background-color:' + color + ';margin-right:5px;"></span>';
                            result += seriesName + ': ' + value.toFixed(4) + '<br/>';
                        });
                        
                        // 计算MACD与Signal线的差距
                        if (macdValue !== null && signalValue !== null) {
                            let diff = macdValue - signalValue;
                            let color = diff >= 0 ? '#d62728' : '#26A69A';
                            result += '<hr style="margin: 5px 0" />';
                            result += '<span style="color:' + color + '">MACD-Signal: ' + diff.toFixed(4) + '</span>';
                        }
                        
                        return result;
                    }
                    """
                ),
            ),
        )
        .set_series_opts(
            markline_opts=opts.MarkLineOpts(
                data=[opts.MarkLineItem(y=0, name="零线")],
                linestyle_opts=opts.LineStyleOpts(color="#888", type_="dashed", width=1)
            )
        )
    )

    # 输出最新日期的指标值 (类似原来的输出)
    latest_signal_value = display_smooth_diff_order1[-1]  # 修改索引访问方式，显示最新数据（最后一个元素）
    latest_histogram_value = display_smoothed_diff[-1]    # 修改索引访问方式，显示最新数据（最后一个元素）
    latest_macd_value = display_ema_diff[-1]              # 修改索引访问方式，显示最新数据（最后一个元素）
    print(f"\nLatest Signal Line value (Order={smooth_order1}): {latest_signal_value:.6f}")
    print(f"Latest Histogram value (Order={smooth_order0}): {latest_histogram_value:.6f}")
    print(f"Latest MACD value (Raw Diff): {latest_macd_value:.6f}")

    # 使用Grid组合多个图表，使用containLabel确保标签包含在布局中
    grid = (
        Grid(init_opts=opts.InitOpts(width="1200px", height="750px", theme="white"))
        .add(
            price_chart,
            grid_opts=opts.GridOpts(
                pos_left="2%",    # 减小左侧边距
                pos_right="2%",   # 减小右侧边距
                pos_top="5%", 
                height="28%",
                is_contain_label=True  # 确保包含Y轴标签
            ),
        )
        .add(
            diff_rate_chart,
            grid_opts=opts.GridOpts(
                pos_left="2%",    # 减小左侧边距
                pos_right="2%",   # 减小右侧边距
                pos_top="35%", 
                height="28%",
                is_contain_label=True  # 确保包含Y轴标签
            ),
        )
        .add(
            macd_chart,
            grid_opts=opts.GridOpts(
                pos_left="2%",    # 减小左侧边距
                pos_right="2%",   # 减小右侧边距
                pos_top="65%", 
                height="30%", 
                is_contain_label=True  # 确保包含所有标签
            ),
        )
    )

    return grid

def save_chart(chart, filename="stock_analysis_chart.html"):
    """
    保存图表到HTML文件
    :param chart: pyecharts图表对象
    :param filename: 输出文件名
    """
    # 设置渲染的HTML页面的配置
    custom_js = """
    <script>
        // 添加水印
        window.addEventListener('load', function() {
            const container = document.querySelector('.container');
            const watermark = document.createElement('div');
            watermark.className = 'watermark';
            watermark.textContent = 'Stock Analysis Engine';
            container.appendChild(watermark);
        });
    </script>
    """
    
    custom_css = """
    <style>
        body {
            font-family: 'Arial', 'Microsoft YaHei', sans-serif;
            background-color: #f7f7f7;
            margin: 0;
            padding: 0;
        }
        .container {
            background-color: white;
            box-shadow: 0 0 15px rgba(0,0,0,0.1);
            border-radius: 5px;
            margin: 20px auto;
            max-width: 1280px;
            position: relative;
            padding: 10px;
        }
        .title {
            text-align: center;
            padding-bottom: 20px;
            color: #333;
        }
        .watermark {
            position: fixed;
            bottom: 20px;
            right: 20px;
            opacity: 0.1;
            font-size: 24px;
            color: #000;
            pointer-events: none;
            transform: rotate(-45deg);
            z-index: 100;
        }
        footer {
            text-align: center;
            margin-top: 20px;
            color: #888;
            font-size: 12px;
        }
    </style>
    """
    
    # 渲染图表为HTML字符串，但不保存
    html_content = chart.render_embed()
    
    # 构建完整的HTML页面内容
    today = datetime.now().strftime('%Y-%m-%d')
    html_page = f"""
    <!DOCTYPE html>
    <html>
    <head>
        <meta charset="UTF-8">
        <meta name="viewport" content="width=device-width, initial-scale=1.0">
        <title>Stock Analysis Chart</title>
        {custom_css}
        <script type="text/javascript" src="https://assets.pyecharts.org/assets/echarts.min.js"></script>
    </head>
    <body>
        <div class="container">
            {html_content}
        </div>
        <footer>
            &copy; {datetime.now().year} Stock Analysis Engine. All rights reserved.
        </footer>
        {custom_js}
    </body>
    </html>
    """
    
    # 将完整的HTML页面保存到文件
    with open(filename, "w", encoding="utf-8") as f:
        f.write(html_page)
    
    print(f"图表已保存到 {filename}")
    
    # 尝试自动打开浏览器显示图表 - 在服务器上可能不起作用
    try:
        if not in_jupyter():
            import webbrowser
            webbrowser.open(filename)
    except:
        pass

# 添加检测是否在Jupyter中的函数
def in_jupyter():
    try:
        from IPython import get_ipython
        if get_ipython() is not None:
            return True
        return False
    except:
        return False

# 修改脚本末尾以使用此函数
if __name__ == "__main__":
    # 创建股票分析图表
    chart = plot_stock_chart_echarts("300522", display_points=200)
    
    # 在Jupyter中显示
    display_chart_in_jupyter(chart)
    
    # 可选：保存为HTML文件
    save_chart(chart, "stock_analysis_chart.html") 