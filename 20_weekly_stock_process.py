#!/usr/bin/env python
# -*- coding: utf-8 -*-

import os
import sys
import subprocess
from datetime import datetime, timedelta
from log_config import setup_logger, log_progress, log_error, log_warning, send_notification
from trade_calendar import is_trade_day, get_latest_trade_day, get_friday_of_current_week

# 获取脚本名称
script_name = os.path.splitext(os.path.basename(__file__))[0]

# 配置日志
logger = setup_logger(script_name)

def should_run_weekly_process():
    """
    判断是否应该运行周线处理流程
    - 如果今天是交易日，返回True
    - 其他情况返回False
    """
    today = datetime.now().date()
    is_today_trade = is_trade_day(today.strftime('%Y-%m-%d'))
    
    # 如果今天是交易日，运行
    if is_today_trade:
        log_progress(logger, f"今天 {today} 是交易日，运行周线处理")
        return True
    
    log_progress(logger, f"今天 {today} 不是交易日，不运行周线处理")
    return False

def run_script(script_path, script_name, args=None):
    """
    运行指定的Python脚本

    Args:
        script_path: 脚本的完整路径
        script_name: 脚本的名称(用于日志和通知)
        args: 命令行参数列表(可选)

    Returns:
        bool: 脚本是否成功执行
    """
    log_progress(logger, f"开始执行 {script_name}")

    try:
        # 准备命令
        command = [sys.executable, script_path]
        if args:
            command.extend(args)

        # 使用subprocess运行脚本，优化超时和缓冲
        result = subprocess.run(
            command,
            check=True,
            capture_output=True,
            text=True,
            timeout=2400  # 30分钟超时
        )

        log_progress(logger, f"{script_name} 执行成功")
        # 只在debug模式下记录详细输出
        if logger.isEnabledFor(10):  # DEBUG level
            logger.debug(f"输出: {result.stdout}")

        return True
    except subprocess.TimeoutExpired as e:
        error_msg = f"{script_name} 执行超时 (30分钟)"
        log_error(logger, error_msg)
        send_notification(
            message=error_msg,
            title=f"股票周线数据处理超时 - {script_name}",
            tags="股票周线数据|处理超时"
        )
        return False
    except subprocess.CalledProcessError as e:
        log_error(logger, f"{script_name} 执行失败: {e}")
        log_error(logger, f"错误输出: {e.stderr}")

        # 发送失败通知
        send_notification(
            message=f"{script_name} 执行过程中发生错误:\n{e.stderr}",
            title=f"股票周线数据处理失败 - {script_name}",
            tags="股票周线数据|处理失败"
        )
        return False
    except Exception as e:
        error_msg = f"{script_name} 执行过程中发生异常: {e}"
        log_error(logger, error_msg)

        # 发送异常通知
        send_notification(
            message=error_msg,
            title=f"股票周线数据处理异常 - {script_name}",
            tags="股票周线数据|系统异常"
        )
        return False

def main():
    """主函数,按顺序执行所有周线处理脚本"""
    # 检查是否需要运行周线处理
    if not should_run_weekly_process():
        log_progress(logger, "根据条件判断,今天不需要运行周线处理流程")
        return True
    
    # 获取当前脚本所在目录的绝对路径
    base_dir = os.path.dirname(os.path.abspath(__file__))
    
    # 定义需要执行的脚本列表
    scripts = [
        {"path": os.path.join(base_dir, "21_stock_week.py"), "name": "股票周线行情数据获取"},
        {"path": os.path.join(base_dir, "22_stock_indicators_week.py"), "name": "股票周线指标计算"},
        {"path": os.path.join(base_dir, "23_macd_divergence_week.py"), "name": "周线MACD背离分析"},
        {"path": os.path.join(base_dir, "24_export_macd_data_week.py"), "name": "周线MACD数据导出"}
    ]
    
    # 记录开始时间
    start_time = datetime.now()
    log_progress(logger, f"开始执行股票周线数据处理流程,时间: {start_time}")
    
    # 依次执行脚本，记录执行状态
    failed_scripts = []
    successful_scripts = []

    for script in scripts:
        success = run_script(script["path"], script["name"])

        if success:
            successful_scripts.append(script["name"])
        else:
            failed_scripts.append(script["name"])
            log_error(logger, f"由于 {script['name']} 执行失败,中断整个周线处理流程")
            break  # 中断流程
    
    # 记录结束时间并计算总耗时
    end_time = datetime.now()
    duration = end_time - start_time

    # 生成执行报告
    if failed_scripts:
        # 有失败的脚本
        failure_message = (
            f"股票周线数据处理流程部分失败\n"
            f"成功执行: {', '.join(successful_scripts)}\n"
            f"失败脚本: {', '.join(failed_scripts)}\n"
            f"开始时间: {start_time.strftime('%Y-%m-%d %H:%M:%S')}\n"
            f"结束时间: {end_time.strftime('%Y-%m-%d %H:%M:%S')}\n"
            f"总耗时: {duration.total_seconds()/60:.2f} 分钟"
        )
        log_error(logger, failure_message)
        return False
    else:
        # 所有脚本执行成功
        success_message = (
            f"股票周线数据处理流程全部完成\n"
            f"成功执行: {', '.join(successful_scripts)}\n"
            f"开始时间: {start_time.strftime('%Y-%m-%d %H:%M:%S')}\n"
            f"结束时间: {end_time.strftime('%Y-%m-%d %H:%M:%S')}\n"
            f"总耗时: {duration.total_seconds()/60:.2f} 分钟"
        )
        log_progress(logger, success_message)
        return True

if __name__ == "__main__":
    try:
        main()
    except Exception as e:
        error_msg = str(e)
        log_error(logger, f"周线处理流程发生未预期的异常: {error_msg}")
        send_notification(
            message=f"周线处理流程发生未预期的异常:\n{error_msg}",
            title="股票周线数据处理发生严重异常",
            tags="股票周线数据|系统异常"
        ) 