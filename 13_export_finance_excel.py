import pandas as pd
from sqlalchemy import create_engine, text
import os
from datetime import datetime
from typing import Optional
from dotenv import load_dotenv
from openpyxl.utils import get_column_letter
import configparser
from log_config import setup_logger, log_progress, log_error, log_warning, send_notification

# 获取脚本名称
script_name = os.path.splitext(os.path.basename(__file__))[0]

# 配置日志
logger = setup_logger(script_name)

def load_config():
    """加载配置文件"""
    config = configparser.ConfigParser()
    config_path = os.path.join(os.path.dirname(__file__), 'config.ini')

    if not os.path.exists(config_path):
        log_error(logger, f"配置文件不存在: {config_path}")
        raise FileNotFoundError(f"配置文件不存在: {config_path}")

    try:
        config.read(config_path, encoding='utf-8')
        log_progress(logger, f"成功加载配置文件: {config_path}")
        return config
    except Exception as e:
        log_error(logger, f"加载配置文件失败: {str(e)}")
        raise

def get_config_value(section, key, fallback=None, value_type='str'):
    """
    获取配置值，支持类型转换

    Args:
        section: 配置节
        key: 配置键
        fallback: 默认值
        value_type: 值类型 ('str', 'int', 'float', 'bool')

    Returns:
        配置值
    """
    try:
        if value_type == 'int':
            return config.getint(section, key, fallback=fallback)
        elif value_type == 'float':
            return config.getfloat(section, key, fallback=fallback)
        elif value_type == 'bool':
            return config.getboolean(section, key, fallback=fallback)
        else:
            return config.get(section, key, fallback=fallback)
    except Exception as e:
        log_warning(logger, f"获取配置值失败 {section}.{key}: {str(e)}，使用默认值 {fallback}")
        return fallback

# 加载配置
config = load_config()

def validate_configuration():
    """验证配置完整性"""
    required_configs = [
        ('database', 'host'),
        ('database', 'port'),
        ('database', 'database'),
        ('export', 'local_dir'),
        ('export', 'server_dir')
    ]

    missing_configs = []
    for section, key in required_configs:
        try:
            value = config.get(section, key)
            if not value or value.strip() == '':
                missing_configs.append(f"{section}.{key}")
        except Exception:
            missing_configs.append(f"{section}.{key}")

    if missing_configs:
        error_msg = f"缺少必需的配置项: {', '.join(missing_configs)}"
        log_error(logger, error_msg)
        raise ValueError(error_msg)

    log_progress(logger, "Excel导出配置验证通过")
    return True

# =============================================================================
# 脚本配置参数类 - Excel导出配置
# =============================================================================
# 这些配置参数从config.ini文件读取，主要是数据库连接和导出路径配置
# 保持了向后兼容性，支持环境变量覆盖

class Config:
    # 数据库配置 - 默认值，优先使用环境变量或配置文件
    DB_URL = None  # 不再使用硬编码的默认值

    # 导出目录配置 - 从配置文件读取
    LOCAL_DIR = get_config_value('export', 'local_dir', '/Users/<USER>/Documents/git/stock-analysis-engine/股票财务指标数据')
    SERVER_DIR = get_config_value('export', 'server_dir', '/home/<USER>/jupyter/stock_data')

    # 数据处理配置
    BATCH_SIZE = 10000  # 数据库分批读取大小，每次读取的记录数
    
    @classmethod
    def get_export_dir(cls):
        # 缓存导出目录选择结果
        if not hasattr(cls, '_export_dir'):
            # 首先检查服务器目录是否存在
            if os.path.exists(cls.SERVER_DIR):
                cls._export_dir = cls.SERVER_DIR
                log_progress(logger, f"使用服务器导出路径: {cls.SERVER_DIR}")
            # 如果服务器目录不存在，检查本地目录
            elif os.path.exists(cls.LOCAL_DIR):
                cls._export_dir = cls.LOCAL_DIR
                log_progress(logger, f"使用本地导出路径: {cls.LOCAL_DIR}")
            else:
                # 如果两个目录都不存在，创建本地目录并使用它
                os.makedirs(cls.LOCAL_DIR, exist_ok=True)
                cls._export_dir = cls.LOCAL_DIR
                log_progress(logger, f"创建并使用本地导出路径: {cls.LOCAL_DIR}")
        return cls._export_dir
    
    @classmethod
    def get_db_url(cls):
        # 优先级: 环境变量 > 配置文件构建 > 默认值
        db_url = os.getenv('DATABASE_URL')
        if db_url:
            return db_url

        # 从配置文件构建数据库URL
        try:
            user = get_config_value('database', 'user', 'root')
            password = get_config_value('database', 'password', '31490600')
            host = get_config_value('database', 'host', '**********')
            port = get_config_value('database', 'port', '3306')
            database = get_config_value('database', 'database', 'instockdb')
            return f"mysql+pymysql://{user}:{password}@{host}:{port}/{database}"
        except Exception:
            return cls.DB_URL

# 定义导出Excel时使用的表头
EXCEL_HEADERS = {
    'stock_code': '股票代码',
    'short_name': '股票简称',
    'industry_name': '行业简称',  
    'report_date': '报告日期',
    'report_type': '报告类型',
    'notice_date': '公布日期',
    'basic_eps': '基本每股收益(元)',
    'diluted_eps': '稀释每股收益(元)',
    'non_gaap_eps': '扣非每股收益(元)',
    'net_asset_ps': '每股净资产(元)',
    'cap_reserve_ps': '每股公积金(元)',
    'undist_profit_ps': '每股未分配利润(元)',
    'oper_cf_ps': '每股经营现金流(元)',
    'total_rev': '营业总收入(元)',
    'gross_profit': '毛利润(元)',
    'net_profit_attr_sh': '归属净利润(元)',
    'non_gaap_net_profit': '扣非净利润(元)',
    'total_rev_yoy_gr': '营业总收入同比增长(%)',
    'net_profit_yoy_gr': '归属净利润同比增长(%)',
    'non_gaap_net_profit_yoy_gr': '扣非净利润同比增长(%)',
    'total_rev_qoq_gr': '营业总收入环比增长(%)',
    'net_profit_qoq_gr': '归属净利润环比增长(%)',
    'non_gaap_net_profit_qoq_gr': '扣非净利润环比增长(%)',
    'roe_wtd': '净资产收益率(加权)(%)',
    'roe_non_gaap_wtd': '净资产收益率(扣非/加权)(%)',
    'roa_wtd': '总资产收益率(加权)(%)',
    'gross_margin': '毛利率(%)',
    'net_margin': '净利率(%)',
    'adv_receipts_to_rev': '预收账款/营业总收入',
    'net_cf_sales_to_rev': '销售净现金流/营业总收入',
    'oper_cf_to_rev': '经营净现金流/营业总收入',
    'eff_tax_rate': '实际税率(%)',
    'curr_ratio': '流动比率',
    'quick_ratio': '速动比率',
    'cash_flow_ratio': '现金流量比率',
    'asset_liab_ratio': '资产负债率(%)',
    'equity_multiplier': '权益系数',
    'equity_ratio': '产权比率',
    'total_asset_turn_days': '总资产周转天数(天)',
    'inv_turn_days': '存货周转天数(天)',
    'acct_recv_turn_days': '应收账款周转天数(天)',
    'total_asset_turn_rate': '总资产周转率(次)',
    'inv_turn_rate': '存货周转率(次)',
    'acct_recv_turn_rate': '应收账款周转率(次)'
}

DIVIDEND_HEADERS = {
    'stock_code': '股票代码',
    'stock_name': '股票名称',
    'end_date': '分红年度',
    'ann_date': '预案公告日',
    'div_proc': '实施进度',
    'stk_div': '每股送转',
    'stk_bo_rate': '每股送股比例',
    'stk_co_rate': '每股转增比例',
    'cash_div': '每股分红(税后)',
    'cash_div_tax': '每股分红(税前)',
    'record_date': '股权登记日',
    'ex_date': '除权除息日',
    'pay_date': '派息日',
    'div_listdate': '红股上市日',
    'imp_ann_date': '实施公告日',
    'base_date': '基准日',
    'base_share': '基准股本(万)'
}

class FinanceExcelExporter:
    def __init__(self, db_url: Optional[str] = None):
        self.db_engine = create_engine(db_url or Config.get_db_url())
        
    def transform_pivot_data(self, df: pd.DataFrame, pivot_fields: Optional[dict] = None, date_field: str = 'end_date', 
                           code_field: str = 'stock_code', name_field: str = 'name') -> pd.DataFrame:
        """
        将指定字段的数据进行透视转换
        :param df: 原始DataFrame
        :param pivot_fields: 需要透视的字段配置,格式为字典 {'field_name': 'display_name'}
        :param date_field: 日期字段名
        :param code_field: 股票代码字段名
        :param name_field: 股票名称字段名
        :return: 转换后的DataFrame
        """
        if pivot_fields is None:
            return df
        
        # 检查列是否存在
        if date_field not in df.columns:
            log_warning(logger, f"列 '{date_field}' 不存在于DataFrame中.可用的列: {df.columns.tolist()}")
            return df
        
        # 将日期字段转换为日期类型
        df[date_field] = pd.to_datetime(df[date_field])
        
        # 创建包含完整日期的列，使用YYYYMMDD格式
        df['period'] = df[date_field].dt.strftime('%Y%m%d')
        
        # 创建基础DataFrame(包含股票代码和名称)
        base_df = df[[code_field, name_field]].drop_duplicates()
        base_df.columns = ['股票代码', '股票简称']
        
        # 获取所有日期期间并排序（从旧到新）
        all_periods = sorted(df['period'].unique())
        
        # 批量透视处理，提高效率
        existing_fields = {field: display_name for field, display_name in pivot_fields.items()
                          if field in df.columns}

        if not existing_fields:
            log_warning(logger, "没有找到可透视的字段")
            return base_df

        # 对每个字段进行透视
        for field, display_name in existing_fields.items():
            try:
                # 对数值进行透视，使用完整日期作为列
                pivot_df = df.pivot(
                    index=code_field,
                    columns='period',
                    values=field
                ).reset_index()

                # 批量重命名日期列
                period_columns = {period: f'{period}{display_name}' for period in all_periods}
                period_columns[code_field] = '股票代码'
                pivot_df = pivot_df.rename(columns=period_columns)

                # 合并到基础DataFrame
                base_df = pd.merge(base_df, pivot_df, on='股票代码', how='left')
            except Exception as e:
                log_warning(logger, f"透视字段 '{field}' 时出错: {str(e)}")
                continue
        
        return base_df

    def get_ordered_columns(self, cols: list, key_cols: list, pivot_fields: dict) -> list:
        """
        按照指标和年份顺序重新排列列
        """
        # 基础列（股票代码、股票简称等）
        final_cols = [col for col in key_cols if col in cols]
        
        # 对每个指标，按日期顺序添加列
        for field, display_name in pivot_fields.items():
            # 找出该指标的所有日期列
            field_cols = [col for col in cols if display_name in col]
            # 根据日期排序（日期在指标名称之前）
            field_cols.sort(key=lambda x: x.split(display_name)[0] if display_name in x else '')
            final_cols.extend(field_cols)
        
        return final_cols

    def export_to_excel(self, output_path: Optional[str] = None, 
                       dividend_pivot_fields: Optional[dict] = None,
                       finance_pivot_fields: Optional[dict] = None) -> None:
        """导出财务数据到Excel文件"""
        try:
            # 修改 SQL 查询，添加 COLLATE 子句来统一校对规则
            query = text("""
                SELECT DISTINCT
                    f.stock_code,
                    f.short_name,
                    i.l2_name as industry_name,
                    f.report_date,
                    f.report_type,
                    f.notice_date,
                    f.basic_eps,
                    f.diluted_eps,
                    f.non_gaap_eps,
                    f.net_asset_ps,
                    f.cap_reserve_ps,
                    f.undist_profit_ps,
                    f.oper_cf_ps,
                    f.total_rev,
                    f.gross_profit,
                    f.net_profit_attr_sh,
                    f.non_gaap_net_profit,
                    f.total_rev_yoy_gr,
                    f.net_profit_yoy_gr,
                    f.non_gaap_net_profit_yoy_gr,
                    f.total_rev_qoq_gr,
                    f.net_profit_qoq_gr,
                    f.non_gaap_net_profit_qoq_gr,
                    f.roe_wtd,
                    f.roe_non_gaap_wtd,
                    f.roa_wtd,
                    f.gross_margin,
                    f.net_margin,
                    f.adv_receipts_to_rev,
                    f.net_cf_sales_to_rev,
                    f.oper_cf_to_rev,
                    f.eff_tax_rate,
                    f.curr_ratio,
                    f.quick_ratio,
                    f.cash_flow_ratio,
                    f.asset_liab_ratio,
                    f.equity_multiplier,
                    f.equity_ratio,
                    f.total_asset_turn_days,
                    f.inv_turn_days,
                    f.acct_recv_turn_days,
                    f.total_asset_turn_rate,
                    f.inv_turn_rate,
                    f.acct_recv_turn_rate
                FROM stock_core_finance f
                LEFT JOIN sw_industry i 
                    ON f.stock_code = SUBSTRING_INDEX(i.ts_code, '.', 1) COLLATE utf8mb4_general_ci
                ORDER BY f.stock_code, f.report_date DESC;
            """)

            # 分红数据查询
            dividend_query = text("""
                SELECT DISTINCT
                    d.stock_code,
                    d.name as stock_name,
                    d.end_date,
                    d.ann_date,
                    d.div_proc,
                    d.stk_div,
                    d.stk_bo_rate,
                    d.stk_co_rate,
                    d.cash_div,
                    d.cash_div_tax,
                    d.record_date,
                    d.ex_date,
                    d.pay_date,
                    d.div_listdate,
                    d.imp_ann_date,
                    d.base_date,
                    d.base_share
                FROM ts_stock_dividend d
                ORDER BY d.stock_code, d.end_date DESC
            """)
            
            # 使用流式查询避免一次性加载全部数据
            with self.db_engine.connect() as conn:
                # 读取核心财务数据
                result_proxy = conn.execution_options(stream_results=True).execute(query)
                
                # 分批读取数据
                df_list = []
                BATCH_SIZE = 10000
                while True:
                    chunk = result_proxy.fetchmany(BATCH_SIZE)
                    if not chunk:
                        break
                    df_chunk = pd.DataFrame(chunk, columns=result_proxy.keys())
                    df_list.append(df_chunk)
                    
                df = pd.concat(df_list) if df_list else pd.DataFrame()

                # 读取分红数据
                dividend_result = conn.execution_options(stream_results=True).execute(dividend_query)
                dividend_df_list = []
                while True:
                    chunk = dividend_result.fetchmany(BATCH_SIZE)
                    if not chunk:
                        break
                    df_chunk = pd.DataFrame(chunk, columns=dividend_result.keys())
                    dividend_df_list.append(df_chunk)

                dividend_df = pd.concat(dividend_df_list) if dividend_df_list else pd.DataFrame()
            
            if df.empty:
                log_warning(logger, "没有核心财务数据可导出")
                return
                
            # 确保DataFrame的列顺序与EXCEL_HEADERS一致
            df = df[list(EXCEL_HEADERS.keys())]
            
            # 创建一个副本用于透视表处理(保留原始列名)
            df_for_pivot = df.copy()
            
            # 重命名列
            df = df.rename(columns=EXCEL_HEADERS)

            # 构建输出路径
            if output_path is None:
                current_date = datetime.now()
                base_export_dir = Config.get_export_dir()
                year_month = current_date.strftime('%Y%m')
                export_dir = os.path.join(base_export_dir, year_month)
                os.makedirs(export_dir, exist_ok=True)
                
                output_path = os.path.join(
                    export_dir,
                    f'股票核心财务指标数据_{current_date.strftime("%Y%m%d")}.xlsx'
                )

            # 检查文件是否存在
            if os.path.exists(output_path):
                try:
                    log_progress(logger, f"删除已存在的文件: {output_path}")
                    os.remove(output_path)
                except Exception as e:
                    log_error(logger, f"删除文件失败: {str(e)}")
                    raise

            # 创建ExcelWriter对象
            with pd.ExcelWriter(output_path, engine='openpyxl') as writer:
                # 导出原始财务数据
                df.to_excel(writer, sheet_name='财务指标数据', index=False)
                
                # 导出原始分红数据
                if not dividend_df.empty:
                    # 创建一个副本用于透视表处理(保留原始列名)
                    dividend_df_for_pivot = dividend_df.copy()
                    
                    # 重命名列用于显示
                    dividend_df = dividend_df[list(DIVIDEND_HEADERS.keys())]
                    dividend_df = dividend_df.rename(columns=DIVIDEND_HEADERS)
                    dividend_df.to_excel(writer, sheet_name='股票分红数据', index=False)
                else:
                    pd.DataFrame(columns=DIVIDEND_HEADERS.values()).to_excel(
                        writer, sheet_name='股票分红数据', index=False
                    )
                
                # 处理财务指标透视
                finance_pivot_df = None
                if finance_pivot_fields and not df.empty:
                    finance_pivot_df = self.transform_pivot_data(
                        df_for_pivot,
                        finance_pivot_fields,
                        date_field='report_date',
                        code_field='stock_code',
                        name_field='short_name'
                    )
                    
                    # 添加行业信息
                    industry_df = df_for_pivot[['stock_code', 'industry_name']].drop_duplicates()
                    industry_df = industry_df.groupby('stock_code').first().reset_index()
                    industry_df.columns = ['股票代码', '所属行业']
                    
                    finance_pivot_df = pd.merge(
                        industry_df,
                        finance_pivot_df,
                        on='股票代码',
                        how='right'
                    )

                # 处理分红数据透视
                dividend_pivot_df = None
                if dividend_pivot_fields and not dividend_df.empty:
                    dividend_pivot_df = self.transform_pivot_data(
                        dividend_df_for_pivot,
                        dividend_pivot_fields,
                        date_field='end_date',
                        code_field='stock_code',
                        name_field='stock_name'
                    )

                # 合并透视表并导出
                if finance_pivot_df is not None and dividend_pivot_df is not None:
                    # 合并透视表
                    merged_pivot_df = pd.merge(
                        finance_pivot_df,
                        dividend_pivot_df.drop(columns=['股票简称']) if '股票简称' in dividend_pivot_df.columns else dividend_pivot_df,
                        on='股票代码',
                        how='outer'
                    )
                    
                    # 重新排序列
                    cols = merged_pivot_df.columns.tolist()
                    key_cols = ['股票代码', '股票简称', '所属行业']
                    
                    # 合并 finance_pivot_fields 和 dividend_pivot_fields
                    all_pivot_fields = {**finance_pivot_fields, **dividend_pivot_fields}
                    final_cols = self.get_ordered_columns(cols, key_cols, all_pivot_fields)
                    
                    # 按照排序后的列重新组织数据
                    merged_pivot_df = merged_pivot_df[final_cols]
                    
                    # 导出合并后的透视表
                    merged_pivot_df.to_excel(writer, sheet_name='透视数据', index=False)

            log_progress(logger, f"数据已导出到: {output_path}")
            
        except Exception as e:
            log_error(logger, f"导出Excel失败: {str(e)}")
            raise

def main():
    # 加载环境变量
    load_dotenv()

    # 验证配置
    validate_configuration()

    try:
        exporter = FinanceExcelExporter()
        
        # 分红数据透视字段 - 这些是原始字段名,不是中文显示名
        dividend_pivot_fields = {
            'cash_div_tax': '每股分红(税前)',
            'stk_div': '每股送转',
            'ex_date': '除权除息日'  # 添加除权除息日字段
        }
        
        # 财务指标透视字段 - 这些是原始字段名,不是中文显示名
        finance_pivot_fields = {
            'basic_eps': '基本每股收益(元)',
            'net_asset_ps': '每股净资产(元)',
            'roe_wtd': '净资产收益率(加权)(%)',
            'roa_wtd': '总资产收益率(加权)(%)',
            'total_rev': '营业总收入(元)',
            'net_profit_attr_sh': '归属净利润(元)'
        }
        
        exporter.export_to_excel(
            dividend_pivot_fields=dividend_pivot_fields,
            finance_pivot_fields=finance_pivot_fields
        )
        log_progress(logger, "Excel导出完成")
        
    except Exception as e:
        error_msg = str(e)
        log_error(logger, f"导出过程失败: {error_msg}")
        # 仅在失败时发送通知
        send_notification(
            message=f"错误详情: {error_msg}", 
            title="财务Excel导出失败",
            tags="财务Excel|异常"
        )
        raise

if __name__ == '__main__':
    main() 