#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
daily_finance_process.py - 按顺序运行财务数据处理脚本

此脚本按以下顺序运行财务数据处理脚本:
1. daily_fina_indicator.py - 获取财务指标数据
2. dividend.py - 获取股息数据
3. export_fiance_data.py - 导出财务数据

同时提供运行状态通知功能.
"""

import os
import sys
import time
import subprocess
import pathlib
from datetime import datetime
from log_config import setup_logger, log_progress, log_error, log_warning, send_notification

# 获取脚本名称
script_name = os.path.splitext(os.path.basename(__file__))[0]

# 配置日志系统
logger = setup_logger(script_name)

def run_script(script_path, script_name, timeout=3600):
    """运行指定的Python脚本并返回执行结果"""
    start_time = time.time()
    log_progress(logger, f"开始运行 {script_name}...")

    def get_duration():
        return time.time() - start_time

    try:
        # 使用subprocess运行脚本，添加超时控制
        result = subprocess.run(
            [sys.executable, script_path],
            capture_output=True,
            text=True,
            check=True,
            timeout=timeout  # 默认1小时超时
        )

        duration = get_duration()
        log_progress(logger, f"{script_name} 运行成功,耗时 {duration:.2f} 秒")

        return {
            "success": True,
            "duration": duration,
            "output": result.stdout,
            "error": result.stderr
        }
    except subprocess.TimeoutExpired as e:
        duration = get_duration()
        error_msg = f"{script_name} 运行超时 ({timeout}秒)"
        log_error(logger, error_msg)

        return {
            "success": False,
            "duration": duration,
            "output": "",
            "error": error_msg
        }
    except subprocess.CalledProcessError as e:
        duration = get_duration()
        log_error(logger, f"{script_name} 运行失败,耗时 {duration:.2f} 秒")
        log_error(logger, f"错误信息: {e.stderr}")

        return {
            "success": False,
            "duration": duration,
            "output": e.stdout or "",
            "error": e.stderr or str(e)
        }
    except Exception as e:
        duration = get_duration()
        error_msg = f"{script_name} 运行出现异常: {str(e)}"
        log_error(logger, f"{error_msg},耗时 {duration:.2f} 秒")

        return {
            "success": False,
            "duration": duration,
            "output": "",
            "error": error_msg
        }

def main():
    """主函数,按顺序运行所有脚本"""
    start_time = time.time()
    current_date = datetime.now().strftime("%Y-%m-%d")
    
    # 定义要运行的脚本列表（优化配置）
    scripts = [
        {
            "path": os.path.join(os.path.dirname(__file__),"11_finance_dividend.py"),
            "name": "股息数据获取",
            "timeout": 600,  # 10分钟超时
            "critical": True  # 关键脚本，失败时停止后续执行
        },
        {
            "path": os.path.join(os.path.dirname(__file__),"12_daily_finance_indicator.py"),
            "name": "财务指标数据获取",
            "timeout": 7200,  # 2小时超时
            "critical": True  # 关键脚本
        },
        {
            "path": os.path.join(os.path.dirname(__file__),"13_export_finance_excel.py"),
            "name": "导出财务数据到Excel",
            "timeout": 300,  # 5分钟超时
            "critical": False  # 非关键脚本，失败时继续执行
        }
    ]
    
    # 存储每个脚本的运行结果
    results = []
    critical_failure = False

    # 按顺序运行脚本
    for script in scripts:
        # 检查脚本文件是否存在
        if not os.path.exists(script["path"]):
            log_error(logger, f"脚本文件不存在: {script['path']}")
            results.append({
                "name": script["name"],
                "result": {
                    "success": False,
                    "duration": 0,
                    "output": "",
                    "error": f"脚本文件不存在: {script['path']}"
                }
            })
            if script.get("critical", True):
                critical_failure = True
                break
            continue

        # 运行脚本
        result = run_script(script["path"], script["name"], script.get("timeout", 3600))
        results.append({
            "name": script["name"],
            "result": result,
            "critical": script.get("critical", True)
        })

        # 如果关键脚本运行失败,发送失败通知并停止后续脚本执行
        if not result["success"] and script.get("critical", True):
            critical_failure = True
            error_message = f"{script['name']}失败\n\n错误信息:\n{result['error']}"

            send_notification(
                message=error_message,
                title=f"财务数据处理失败 - {current_date}",
                tags="财务数据|处理失败"
            )

            log_error(logger, f"由于关键脚本 {script['name']} 失败,停止后续脚本执行")
            break
        elif not result["success"]:
            # 非关键脚本失败，记录警告但继续执行
            log_warning(logger, f"非关键脚本 {script['name']} 失败，继续执行后续脚本")
            log_warning(logger, f"失败原因: {result['error']}")
    
    # 计算总耗时
    end_time = time.time()
    total_duration = end_time - start_time
    
    # 生成结果报告
    successful_scripts = [item for item in results if item["result"]["success"]]
    failed_scripts = [item for item in results if not item["result"]["success"]]

    report = f"财务数据处理完成报告 - {current_date}\n\n"
    report += f"总耗时: {total_duration:.2f} 秒\n"
    report += f"成功脚本: {len(successful_scripts)}/{len(results)}\n\n"

    for item in results:
        status = "✅成功" if item["result"]["success"] else "❌失败"
        critical_mark = "🔴" if item.get("critical", True) else "🟡"
        report += f"{critical_mark} {item['name']}: {status}, 耗时 {item['result']['duration']:.2f} 秒\n"
        if not item["result"]["success"]:
            report += f"   错误: {item['result']['error'][:100]}...\n"

    # 判断整体执行状态
    if not critical_failure and len(failed_scripts) == 0:
        log_progress(logger, "所有脚本运行成功")
        log_progress(logger, report)
    elif not critical_failure:
        log_warning(logger, f"部分非关键脚本失败，但整体流程完成")
        log_progress(logger, report)
    else:
        log_error(logger, "关键脚本失败，流程中断")
        log_error(logger, report)

    log_progress(logger, f"财务数据处理流程结束,总耗时 {total_duration:.2f} 秒")

if __name__ == "__main__":
    try:
        main()
    except Exception as e:
        log_error(logger, f"财务数据处理主程序发生异常:\n\n{str(e)}")
        send_notification(
            message=f"财务数据处理主程序发生异常:\n\n{str(e)}",
            title=f"财务数据处理异常 - {datetime.now().strftime('%Y-%m-%d')}",
            tags="异常"
        ) 